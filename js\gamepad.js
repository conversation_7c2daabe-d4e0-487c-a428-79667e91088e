// Gamepad Controller Manager
class GamepadManager {
    constructor() {
        this.gamepads = {};
        this.connected = false;
        this.deadzone = 0.15; // منطقة ميتة للعصا التناظرية
        this.buttonPressed = {};
        this.axisValues = { x: 0, y: 0 };
        this.vibrationSupported = false;
        
        this.setupEventListeners();
        this.startPolling();
    }
    
    setupEventListeners() {
        window.addEventListener('gamepadconnected', (e) => {
            this.onGamepadConnected(e.gamepad);
        });
        
        window.addEventListener('gamepaddisconnected', (e) => {
            this.onGamepadDisconnected(e.gamepad);
        });
    }
    
    onGamepadConnected(gamepad) {
        console.log(`🎮 تم توصيل الجويستيك: ${gamepad.id}`);
        this.gamepads[gamepad.index] = gamepad;
        this.connected = true;
        this.vibrationSupported = gamepad.vibrationActuator !== undefined;
        
        // إظهار إشعار للمستخدم
        if (window.game && window.game.ui) {
            window.game.ui.addNotification('🎮 تم توصيل الجويستيك!', 'success', 3000);
        }
        
        // تحديث واجهة المستخدم
        this.updateGamepadUI(true);
    }
    
    onGamepadDisconnected(gamepad) {
        console.log(`🎮 تم قطع الجويستيك: ${gamepad.id}`);
        delete this.gamepads[gamepad.index];
        this.connected = Object.keys(this.gamepads).length > 0;
        
        if (window.game && window.game.ui) {
            window.game.ui.addNotification('🎮 تم قطع الجويستيك', 'info', 3000);
        }
        
        this.updateGamepadUI(false);
    }
    
    startPolling() {
        // تحديث حالة الجويستيك كل إطار
        const poll = () => {
            this.updateGamepads();
            requestAnimationFrame(poll);
        };
        requestAnimationFrame(poll);
    }
    
    updateGamepads() {
        const gamepads = navigator.getGamepads();
        
        for (let i = 0; i < gamepads.length; i++) {
            if (gamepads[i]) {
                this.gamepads[i] = gamepads[i];
            }
        }
        
        if (this.connected) {
            this.processInput();
        }
    }
    
    processInput() {
        const gamepad = this.getActiveGamepad();
        if (!gamepad) return;
        
        // معالجة العصا التناظرية اليسرى (الحركة)
        const leftStickX = gamepad.axes[0];
        const leftStickY = gamepad.axes[1];
        
        // تطبيق المنطقة الميتة
        this.axisValues.x = Math.abs(leftStickX) > this.deadzone ? leftStickX : 0;
        this.axisValues.y = Math.abs(leftStickY) > this.deadzone ? leftStickY : 0;
        
        // معالجة الأزرار
        this.processButtons(gamepad);
    }
    
    processButtons(gamepad) {
        const buttons = gamepad.buttons;
        
        // خريطة الأزرار (Xbox/PlayStation layout)
        const buttonMap = {
            0: 'A', // X على PlayStation
            1: 'B', // Circle على PlayStation  
            2: 'X', // Square على PlayStation
            3: 'Y', // Triangle على PlayStation
            4: 'LB', // L1 على PlayStation
            5: 'RB', // R1 على PlayStation
            6: 'LT', // L2 على PlayStation
            7: 'RT', // R2 على PlayStation
            8: 'Back', // Share على PlayStation
            9: 'Start', // Options على PlayStation
            10: 'LS', // L3 على PlayStation
            11: 'RS', // R3 على PlayStation
            12: 'Up', // D-Pad Up
            13: 'Down', // D-Pad Down
            14: 'Left', // D-Pad Left
            15: 'Right', // D-Pad Right
            16: 'Home' // PS Button على PlayStation
        };
        
        for (let i = 0; i < buttons.length; i++) {
            const button = buttons[i];
            const buttonName = buttonMap[i] || `Button${i}`;
            const isPressed = button.pressed;
            const wasPressed = this.buttonPressed[buttonName] || false;
            
            // كشف الضغط الجديد (edge detection)
            if (isPressed && !wasPressed) {
                this.onButtonPress(buttonName);
            } else if (!isPressed && wasPressed) {
                this.onButtonRelease(buttonName);
            }
            
            this.buttonPressed[buttonName] = isPressed;
        }
    }
    
    onButtonPress(buttonName) {
        console.log(`🎮 تم الضغط على: ${buttonName}`);
        
        // تنفيذ الإجراءات حسب الزر
        switch (buttonName) {
            case 'A': // زر التأكيد
                this.handleConfirmButton();
                break;
            case 'B': // زر الإلغاء/الرجوع
                this.handleCancelButton();
                break;
            case 'X': // زر النيتروس
                this.handleNitroButton(true);
                break;
            case 'Y': // زر خاص
                this.handleSpecialButton();
                break;
            case 'Start': // زر الإيقاف/التشغيل
                this.handlePauseButton();
                break;
            case 'Back': // زر الخيارات
                this.handleOptionsButton();
                break;
            case 'LB':
            case 'RB':
                this.handleShoulderButton(buttonName);
                break;
            case 'Home':
                this.handleHomeButton();
                break;
        }
    }
    
    onButtonRelease(buttonName) {
        switch (buttonName) {
            case 'X': // إيقاف النيتروس
                this.handleNitroButton(false);
                break;
        }
    }
    
    handleConfirmButton() {
        // زر التأكيد - بدء اللعبة أو إعادة التشغيل
        if (window.game) {
            const gameState = window.game.gameState;
            if (gameState === 'start') {
                document.getElementById('startBtn')?.click();
            } else if (gameState === 'gameOver') {
                document.getElementById('restartBtn')?.click();
            }
        }
    }
    
    handleCancelButton() {
        // زر الإلغاء - الخروج من الشاشة الكاملة أو الإيقاف
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else if (window.game && window.game.gameState === 'playing') {
            window.game.pauseGame();
        }
    }
    
    handleNitroButton(pressed) {
        // زر النيتروس
        if (window.game && window.game.keys) {
            window.game.keys['shift'] = pressed;
        }
    }
    
    handleSpecialButton() {
        // زر خاص - تبديل الشاشة الكاملة
        this.toggleFullscreen();
    }
    
    handlePauseButton() {
        // زر الإيقاف/التشغيل
        if (window.game) {
            const gameState = window.game.gameState;
            if (gameState === 'playing') {
                window.game.pauseGame();
            } else if (gameState === 'paused') {
                window.game.resumeGame();
            }
        }
    }
    
    handleOptionsButton() {
        // زر الخيارات - تبديل الصوت
        if (window.audioManager) {
            window.audioManager.toggleSound();
        }
    }
    
    handleShoulderButton(buttonName) {
        // أزرار الكتف - تغيير الصعوبة أو خيارات أخرى
        console.log(`تم الضغط على زر الكتف: ${buttonName}`);
    }
    
    handleHomeButton() {
        // زر الهوم - العودة للقائمة الرئيسية
        if (window.game && window.game.gameState === 'playing') {
            window.game.pauseGame();
        }
    }
    
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('خطأ في الشاشة الكاملة:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }
    
    // الحصول على الجويستيك النشط
    getActiveGamepad() {
        const gamepadIndices = Object.keys(this.gamepads);
        if (gamepadIndices.length > 0) {
            return this.gamepads[gamepadIndices[0]];
        }
        return null;
    }
    
    // الحصول على قيم العصا التناظرية
    getAxisValues() {
        return this.axisValues;
    }
    
    // فحص حالة زر معين
    isButtonPressed(buttonName) {
        return this.buttonPressed[buttonName] || false;
    }
    
    // اهتزاز الجويستيك (إذا كان مدعوماً)
    vibrate(duration = 200, strongMagnitude = 1.0, weakMagnitude = 0.5) {
        if (!this.vibrationSupported) return;
        
        const gamepad = this.getActiveGamepad();
        if (gamepad && gamepad.vibrationActuator) {
            gamepad.vibrationActuator.playEffect('dual-rumble', {
                duration: duration,
                strongMagnitude: strongMagnitude,
                weakMagnitude: weakMagnitude
            });
        }
    }
    
    // تحديث واجهة المستخدم لإظهار حالة الجويستيك
    updateGamepadUI(connected) {
        const gamepadIndicator = document.getElementById('gamepadIndicator');
        if (gamepadIndicator) {
            gamepadIndicator.style.display = connected ? 'block' : 'none';
            gamepadIndicator.textContent = connected ? '🎮 متصل' : '🎮 غير متصل';
        }

        // إظهار/إخفاء معلومات الجويستيك في القائمة الرئيسية
        const gamepadInfo = document.getElementById('gamepadInfo');
        if (gamepadInfo) {
            gamepadInfo.style.display = connected ? 'block' : 'none';
        }

        // تحديث مساعدة الجويستيك في الشاشة الكاملة
        if (window.fullscreenManager) {
            window.fullscreenManager.updateGamepadHelp();
        }
    }
    
    // الحصول على معلومات الجويستيك
    getGamepadInfo() {
        const gamepad = this.getActiveGamepad();
        if (gamepad) {
            return {
                id: gamepad.id,
                connected: this.connected,
                buttonsCount: gamepad.buttons.length,
                axesCount: gamepad.axes.length,
                vibrationSupported: this.vibrationSupported
            };
        }
        return null;
    }
}
