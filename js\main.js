// Main entry point for the game
let game;

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    initializeGame();
});

function initializeGame() {
    try {
        // Create the main game instance
        game = new Game();
        
        // Add some debug information
        console.log('🏎️ لعبة السيارات الاحترافية تم تحميلها بنجاح!');
        console.log('استخدم الأسهم أو WASD للتحكم');
        console.log('اضغط Shift للنيتروس');
        console.log('اضغط مسافة للإيقاف/التشغيل');
        
        // Add performance monitoring
        if (window.performance) {
            const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
            console.log(`⚡ وقت التحميل: ${loadTime}ms`);
        }
        
        // Add error handling for the game
        window.addEventListener('error', handleGameError);
        window.addEventListener('unhandledrejection', handleGameError);
        
    } catch (error) {
        console.error('خطأ في تشغيل اللعبة:', error);
        showErrorMessage('حدث خطأ في تشغيل اللعبة. يرجى إعادة تحميل الصفحة.');
    }
}

function handleGameError(event) {
    console.error('خطأ في اللعبة:', event.error || event.reason);
    
    // Try to recover gracefully
    if (game && game.gameState === GAME_STATES.PLAYING) {
        game.pauseGame();
        showErrorMessage('حدث خطأ مؤقت. تم إيقاف اللعبة مؤقتاً.');
    }
}

function showErrorMessage(message) {
    // Create error notification
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-notification';
    errorDiv.innerHTML = `
        <div class="error-content">
            <h3>⚠️ تنبيه</h3>
            <p>${message}</p>
            <button onclick="this.parentElement.parentElement.remove()">موافق</button>
        </div>
    `;
    
    // Add styles
    errorDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-family: Arial, sans-serif;
    `;
    
    errorDiv.querySelector('.error-content').style.cssText = `
        background: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        color: black;
        max-width: 400px;
    `;
    
    errorDiv.querySelector('button').style.cssText = `
        background: #e74c3c;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 10px;
    `;
    
    document.body.appendChild(errorDiv);
}

// Performance optimization
function optimizePerformance() {
    // Reduce particle count on slower devices
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
        CONFIG.PARTICLES.EXPLOSION.COUNT = Math.floor(CONFIG.PARTICLES.EXPLOSION.COUNT * 0.7);
        CONFIG.PARTICLES.EXHAUST.COUNT = Math.floor(CONFIG.PARTICLES.EXHAUST.COUNT * 0.7);
        CONFIG.PARTICLES.SPARKS.COUNT = Math.floor(CONFIG.PARTICLES.SPARKS.COUNT * 0.7);
    }
    
    // Adjust quality based on screen size
    if (window.innerWidth < 768) {
        CONFIG.WEATHER.RAIN.DROP_COUNT = Math.floor(CONFIG.WEATHER.RAIN.DROP_COUNT * 0.6);
    }
}

// Call performance optimization
optimizePerformance();

// Utility functions for debugging
window.gameDebug = {
    getGameState: () => game ? game.gameState : 'Game not initialized',
    getScore: () => game ? game.score : 0,
    getLevel: () => game ? game.level : 0,
    getPlayerHealth: () => game ? game.player.health : 0,
    getPlayerFuel: () => game ? game.player.fuel : 0,
    getEnemyCount: () => game ? game.enemyManager.getEnemyCount() : 0,
    toggleGodMode: () => {
        if (game && game.player) {
            game.player.health = 1000;
            game.player.fuel = 1000;
            game.player.nitro = 1000;
            console.log('🛡️ وضع الحماية مفعل!');
        }
    },
    addScore: (amount) => {
        if (game) {
            game.score += amount;
            console.log(`💰 تم إضافة ${amount} نقطة`);
        }
    }
};

// Add mobile touch controls
if ('ontouchstart' in window) {
    addMobileTouchControls();
}

function addMobileTouchControls() {
    // Create virtual joystick for mobile
    const joystickContainer = document.createElement('div');
    joystickContainer.className = 'mobile-controls';
    joystickContainer.innerHTML = `
        <div class="virtual-joystick">
            <div class="joystick-base">
                <div class="joystick-stick" id="joystickStick"></div>
            </div>
        </div>
        <div class="mobile-buttons">
            <button class="mobile-btn" id="nitroBtn">🚀</button>
            <button class="mobile-btn" id="pauseBtn">⏸️</button>
        </div>
    `;
    
    // Add mobile controls styles
    const mobileStyles = document.createElement('style');
    mobileStyles.textContent = `
        .mobile-controls {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            display: none;
            justify-content: space-between;
            align-items: flex-end;
            z-index: 1000;
            pointer-events: none;
        }
        
        .virtual-joystick {
            pointer-events: all;
        }
        
        .joystick-base {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            position: relative;
            border: 2px solid rgba(255, 255, 255, 0.5);
        }
        
        .joystick-stick {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.8);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.1s ease;
        }
        
        .mobile-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            pointer-events: all;
        }
        
        .mobile-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            background: rgba(255, 255, 255, 0.3);
            color: white;
            font-size: 24px;
            cursor: pointer;
            border: 2px solid rgba(255, 255, 255, 0.5);
        }
        
        @media (max-width: 768px) {
            .mobile-controls {
                display: flex;
            }
        }
    `;
    
    document.head.appendChild(mobileStyles);
    document.body.appendChild(joystickContainer);
    
    // Add touch event listeners for virtual joystick
    setupVirtualJoystick();
}

function setupVirtualJoystick() {
    const joystickBase = document.querySelector('.joystick-base');
    const joystickStick = document.querySelector('.joystick-stick');
    const nitroBtn = document.getElementById('nitroBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    
    if (!joystickBase || !joystickStick) return;
    
    let isDragging = false;
    let startPos = { x: 0, y: 0 };
    
    // Joystick events
    joystickBase.addEventListener('touchstart', (e) => {
        e.preventDefault();
        isDragging = true;
        const rect = joystickBase.getBoundingClientRect();
        startPos.x = rect.left + rect.width / 2;
        startPos.y = rect.top + rect.height / 2;
    });
    
    document.addEventListener('touchmove', (e) => {
        if (!isDragging || !game) return;
        e.preventDefault();
        
        const touch = e.touches[0];
        const deltaX = touch.clientX - startPos.x;
        const deltaY = touch.clientY - startPos.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const maxDistance = 25;
        
        if (distance <= maxDistance) {
            joystickStick.style.transform = `translate(${deltaX - 15}px, ${deltaY - 15}px)`;
        } else {
            const angle = Math.atan2(deltaY, deltaX);
            const x = Math.cos(angle) * maxDistance;
            const y = Math.sin(angle) * maxDistance;
            joystickStick.style.transform = `translate(${x - 15}px, ${y - 15}px)`;
        }
        
        // Convert to game input
        const normalizedX = Math.max(-1, Math.min(1, deltaX / maxDistance));
        const normalizedY = Math.max(-1, Math.min(1, deltaY / maxDistance));
        
        // Update game keys based on joystick position
        game.keys['a'] = normalizedX < -0.3;
        game.keys['d'] = normalizedX > 0.3;
        game.keys['w'] = normalizedY < -0.3;
        game.keys['s'] = normalizedY > 0.3;
    });
    
    document.addEventListener('touchend', () => {
        if (!isDragging) return;
        isDragging = false;
        joystickStick.style.transform = 'translate(-50%, -50%)';
        
        // Clear all movement keys
        if (game) {
            game.keys['a'] = false;
            game.keys['d'] = false;
            game.keys['w'] = false;
            game.keys['s'] = false;
        }
    });
    
    // Button events
    if (nitroBtn) {
        nitroBtn.addEventListener('touchstart', (e) => {
            e.preventDefault();
            if (game) game.keys['shift'] = true;
        });
        
        nitroBtn.addEventListener('touchend', (e) => {
            e.preventDefault();
            if (game) game.keys['shift'] = false;
        });
    }
    
    if (pauseBtn) {
        pauseBtn.addEventListener('touchstart', (e) => {
            e.preventDefault();
            if (game) {
                if (game.gameState === GAME_STATES.PLAYING) {
                    game.pauseGame();
                } else if (game.gameState === GAME_STATES.PAUSED) {
                    game.resumeGame();
                }
            }
        });
    }
}

// Add fullscreen support
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
            console.log('Fullscreen not supported:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

// Add keyboard shortcut for fullscreen
document.addEventListener('keydown', (e) => {
    if (e.key === 'F11') {
        e.preventDefault();
        toggleFullscreen();
    }
});

console.log('🎮 لعبة السيارات الاحترافية جاهزة للعب!');
console.log('💡 نصائح:');
console.log('- استخدم F11 للشاشة الكاملة');
console.log('- اكتب gameDebug في وحدة التحكم للحصول على أدوات التطوير');
console.log('- اللعبة تدعم اللمس على الأجهزة المحمولة');
