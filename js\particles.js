// Particle System
class Particle {
    constructor(x, y, vx, vy, color, life, size = 3) {
        this.x = x;
        this.y = y;
        this.vx = vx;
        this.vy = vy;
        this.color = color;
        this.life = life;
        this.maxLife = life;
        this.size = size;
        this.gravity = 0.1;
        this.friction = 0.98;
    }
    
    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.vy += this.gravity;
        this.vx *= this.friction;
        this.vy *= this.friction;
        this.life--;
        
        return this.life > 0;
    }
    
    draw(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size * alpha, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

class ParticleSystem {
    constructor() {
        this.particles = [];
    }
    
    createExplosion(x, y, config = CONFIG.PARTICLES.EXPLOSION) {
        for (let i = 0; i < config.COUNT; i++) {
            const angle = (Math.PI * 2 * i) / config.COUNT;
            const speed = config.SPEED + Math.random() * config.SPEED;
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed;
            const color = config.COLORS[Math.floor(Math.random() * config.COLORS.length)];
            const life = config.LIFE + Math.random() * 10;
            const size = 2 + Math.random() * 4;
            
            this.particles.push(new Particle(x, y, vx, vy, color, life, size));
        }
    }
    
    createExhaust(x, y, config = CONFIG.PARTICLES.EXHAUST) {
        for (let i = 0; i < config.COUNT; i++) {
            const vx = (Math.random() - 0.5) * 2;
            const vy = config.SPEED + Math.random() * 2;
            const color = config.COLORS[Math.floor(Math.random() * config.COLORS.length)];
            const life = config.LIFE + Math.random() * 10;
            const size = 1 + Math.random() * 2;
            
            this.particles.push(new Particle(x, y, vx, vy, color, life, size));
        }
    }
    
    createSparks(x, y, config = CONFIG.PARTICLES.SPARKS) {
        for (let i = 0; i < config.COUNT; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = config.SPEED + Math.random() * config.SPEED;
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed;
            const color = config.COLORS[Math.floor(Math.random() * config.COLORS.length)];
            const life = config.LIFE + Math.random() * 5;
            const size = 1 + Math.random() * 2;
            
            this.particles.push(new Particle(x, y, vx, vy, color, life, size));
        }
    }
    
    createNitroFlame(x, y, config = CONFIG.PARTICLES.NITRO) {
        for (let i = 0; i < config.COUNT; i++) {
            const vx = (Math.random() - 0.5) * 3;
            const vy = config.SPEED + Math.random() * 3;
            const color = config.COLORS[Math.floor(Math.random() * config.COLORS.length)];
            const life = config.LIFE + Math.random() * 10;
            const size = 2 + Math.random() * 3;
            
            this.particles.push(new Particle(x, y, vx, vy, color, life, size));
        }
    }
    
    createRainDrop(x, y) {
        const vx = (Math.random() - 0.5) * 2;
        const vy = 5 + Math.random() * 3;
        const color = '#87ceeb';
        const life = 30 + Math.random() * 20;
        const size = 1 + Math.random();
        
        this.particles.push(new Particle(x, y, vx, vy, color, life, size));
    }
    
    createCoinSparkle(x, y) {
        for (let i = 0; i < 5; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = 1 + Math.random() * 2;
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed;
            const color = '#f1c40f';
            const life = 20 + Math.random() * 10;
            const size = 1 + Math.random();
            
            this.particles.push(new Particle(x, y, vx, vy, color, life, size));
        }
    }
    
    update() {
        this.particles = this.particles.filter(particle => particle.update());
    }
    
    draw(ctx) {
        this.particles.forEach(particle => particle.draw(ctx));
    }
    
    clear() {
        this.particles = [];
    }
}

// Weather Effects
class WeatherSystem {
    constructor() {
        this.rainDrops = [];
        this.fogOpacity = 0;
        this.weatherType = 'clear'; // 'clear', 'rain', 'fog'
        this.weatherTimer = 0;
        this.weatherDuration = 0;
    }
    
    startRain() {
        this.weatherType = 'rain';
        this.weatherDuration = 300 + Math.random() * 600; // 5-15 seconds
        this.weatherTimer = 0;
    }
    
    startFog() {
        this.weatherType = 'fog';
        this.weatherDuration = 200 + Math.random() * 400; // 3-10 seconds
        this.weatherTimer = 0;
        this.fogOpacity = 0;
    }
    
    clearWeather() {
        this.weatherType = 'clear';
        this.fogOpacity = 0;
        this.rainDrops = [];
    }
    
    update() {
        this.weatherTimer++;
        
        if (this.weatherTimer > this.weatherDuration) {
            this.clearWeather();
        }
        
        switch (this.weatherType) {
            case 'rain':
                this.updateRain();
                break;
            case 'fog':
                this.updateFog();
                break;
        }
        
        // Random weather changes
        if (this.weatherType === 'clear' && Math.random() < 0.001) {
            const weatherTypes = ['rain', 'fog'];
            const randomWeather = weatherTypes[Math.floor(Math.random() * weatherTypes.length)];
            
            if (randomWeather === 'rain') {
                this.startRain();
            } else if (randomWeather === 'fog') {
                this.startFog();
            }
        }
    }
    
    updateRain() {
        // Create new raindrops
        for (let i = 0; i < CONFIG.WEATHER.RAIN.DROP_COUNT; i++) {
            if (Math.random() < 0.3) {
                this.rainDrops.push({
                    x: Math.random() * CONFIG.CANVAS.WIDTH,
                    y: -10,
                    speed: CONFIG.WEATHER.RAIN.DROP_SPEED + Math.random() * 3,
                    length: 5 + Math.random() * 10
                });
            }
        }
        
        // Update existing raindrops
        this.rainDrops = this.rainDrops.filter(drop => {
            drop.y += drop.speed;
            return drop.y < CONFIG.CANVAS.HEIGHT + 20;
        });
    }
    
    updateFog() {
        const targetOpacity = CONFIG.WEATHER.FOG.OPACITY;
        if (this.fogOpacity < targetOpacity) {
            this.fogOpacity += 0.01;
        }
    }
    
    draw(ctx) {
        switch (this.weatherType) {
            case 'rain':
                this.drawRain(ctx);
                break;
            case 'fog':
                this.drawFog(ctx);
                break;
        }
    }
    
    drawRain(ctx) {
        ctx.save();
        ctx.strokeStyle = 'rgba(135, 206, 235, 0.6)';
        ctx.lineWidth = 1;
        
        this.rainDrops.forEach(drop => {
            ctx.beginPath();
            ctx.moveTo(drop.x, drop.y);
            ctx.lineTo(drop.x - 2, drop.y - drop.length);
            ctx.stroke();
        });
        
        ctx.restore();
    }
    
    drawFog(ctx) {
        if (this.fogOpacity > 0) {
            ctx.save();
            ctx.fillStyle = `rgba(200, 200, 200, ${this.fogOpacity})`;
            ctx.fillRect(0, 0, CONFIG.CANVAS.WIDTH, CONFIG.CANVAS.HEIGHT);
            ctx.restore();
        }
    }
    
    getVisibilityMultiplier() {
        switch (this.weatherType) {
            case 'rain':
                return CONFIG.WEATHER.RAIN.VISIBILITY_REDUCTION;
            case 'fog':
                return CONFIG.WEATHER.FOG.VISIBILITY_REDUCTION;
            default:
                return 1.0;
        }
    }
}
