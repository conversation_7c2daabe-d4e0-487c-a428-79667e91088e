// Audio Manager
class AudioManager {
    constructor() {
        this.sounds = {};
        this.music = {};
        this.soundEnabled = true;
        this.musicEnabled = true;
        this.masterVolume = CONFIG.AUDIO.MASTER_VOLUME;
        this.sfxVolume = CONFIG.AUDIO.SFX_VOLUME;
        this.musicVolume = CONFIG.AUDIO.MUSIC_VOLUME;
        
        this.initializeAudio();
        this.setupEventListeners();
    }
    
    initializeAudio() {
        // Initialize sound effects
        this.sounds.engine = document.getElementById('engineSound');
        this.sounds.crash = document.getElementById('crashSound');
        this.sounds.coin = document.getElementById('coinSound');
        this.sounds.nitro = document.getElementById('nitroSound');
        
        // Initialize background music
        this.music.background = document.getElementById('backgroundMusic');
        
        // Set initial volumes
        this.updateVolumes();
        
        // Create synthetic sounds if audio files are not available
        this.createSyntheticSounds();
    }
    
    createSyntheticSounds() {
        // Create audio context for synthetic sounds
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.createEngineSound();
        } catch (e) {
            console.log('Web Audio API not supported');
        }
    }
    
    createEngineSound() {
        if (!this.audioContext) return;
        
        this.engineOscillator = null;
        this.engineGain = this.audioContext.createGain();
        this.engineGain.connect(this.audioContext.destination);
        this.engineGain.gain.value = 0;
    }
    
    startEngineSound() {
        if (!this.audioContext || !this.soundEnabled) return;
        
        if (this.engineOscillator) {
            this.stopEngineSound();
        }
        
        this.engineOscillator = this.audioContext.createOscillator();
        this.engineOscillator.type = 'sawtooth';
        this.engineOscillator.frequency.setValueAtTime(80, this.audioContext.currentTime);
        this.engineOscillator.connect(this.engineGain);
        this.engineOscillator.start();
        
        this.engineGain.gain.setValueAtTime(0, this.audioContext.currentTime);
        this.engineGain.gain.linearRampToValueAtTime(0.1 * this.sfxVolume, this.audioContext.currentTime + 0.1);
    }
    
    updateEngineSound(speed) {
        if (!this.engineOscillator || !this.soundEnabled) return;
        
        const frequency = 80 + (speed * 20);
        const volume = 0.05 + (speed * 0.02);
        
        this.engineOscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        this.engineGain.gain.setValueAtTime(volume * this.sfxVolume, this.audioContext.currentTime);
    }
    
    stopEngineSound() {
        if (!this.engineOscillator) return;
        
        this.engineGain.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + 0.1);
        setTimeout(() => {
            if (this.engineOscillator) {
                this.engineOscillator.stop();
                this.engineOscillator = null;
            }
        }, 100);
    }
    
    playSound(soundName, volume = 1) {
        if (!this.soundEnabled) return;
        
        switch (soundName) {
            case 'crash':
                this.playCrashSound();
                break;
            case 'coin':
                this.playCoinSound();
                break;
            case 'nitro':
                this.playNitroSound();
                break;
            case 'brake':
                this.playBrakeSound();
                break;
            default:
                if (this.sounds[soundName]) {
                    this.sounds[soundName].volume = volume * this.sfxVolume * this.masterVolume;
                    this.sounds[soundName].currentTime = 0;
                    this.sounds[soundName].play().catch(e => console.log('Audio play failed:', e));
                }
        }
    }
    
    playCrashSound() {
        if (!this.audioContext || !this.soundEnabled) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.type = 'square';
        oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(50, this.audioContext.currentTime + 0.5);
        
        gainNode.gain.setValueAtTime(0.3 * this.sfxVolume, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.5);
        
        oscillator.start();
        oscillator.stop(this.audioContext.currentTime + 0.5);
    }
    
    playCoinSound() {
        if (!this.audioContext || !this.soundEnabled) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
        oscillator.frequency.setValueAtTime(1000, this.audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.2 * this.sfxVolume, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.2);
        
        oscillator.start();
        oscillator.stop(this.audioContext.currentTime + 0.2);
    }
    
    playNitroSound() {
        if (!this.audioContext || !this.soundEnabled) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.type = 'sawtooth';
        oscillator.frequency.setValueAtTime(150, this.audioContext.currentTime);
        oscillator.frequency.linearRampToValueAtTime(300, this.audioContext.currentTime + 1);
        
        gainNode.gain.setValueAtTime(0.15 * this.sfxVolume, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.01, this.audioContext.currentTime + 1);
        
        oscillator.start();
        oscillator.stop(this.audioContext.currentTime + 1);
    }
    
    playBrakeSound() {
        if (!this.audioContext || !this.soundEnabled) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.type = 'square';
        oscillator.frequency.setValueAtTime(100, this.audioContext.currentTime);
        
        gainNode.gain.setValueAtTime(0.1 * this.sfxVolume, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
        
        oscillator.start();
        oscillator.stop(this.audioContext.currentTime + 0.3);
    }
    
    playMusic(musicName) {
        if (!this.musicEnabled) return;
        
        if (this.music[musicName]) {
            this.music[musicName].volume = this.musicVolume * this.masterVolume;
            this.music[musicName].play().catch(e => console.log('Music play failed:', e));
        }
    }
    
    stopMusic(musicName) {
        if (this.music[musicName]) {
            this.music[musicName].pause();
            this.music[musicName].currentTime = 0;
        }
    }
    
    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        if (!this.soundEnabled) {
            this.stopEngineSound();
        }
        return this.soundEnabled;
    }
    
    toggleMusic() {
        this.musicEnabled = !this.musicEnabled;
        if (!this.musicEnabled) {
            this.stopMusic('background');
        } else {
            this.playMusic('background');
        }
        return this.musicEnabled;
    }
    
    updateVolumes() {
        Object.values(this.sounds).forEach(sound => {
            if (sound) sound.volume = this.sfxVolume * this.masterVolume;
        });
        
        Object.values(this.music).forEach(music => {
            if (music) music.volume = this.musicVolume * this.masterVolume;
        });
    }
    
    setupEventListeners() {
        // Resume audio context on user interaction
        document.addEventListener('click', () => {
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
        }, { once: true });
    }
}
