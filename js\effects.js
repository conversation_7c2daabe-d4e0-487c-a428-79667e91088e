// Visual Effects Manager
class EffectsManager {
    constructor() {
        this.screenShake = { intensity: 0, duration: 0 };
        this.flashEffect = { opacity: 0, color: '#ffffff' };
        this.slowMotion = { active: false, duration: 0, factor: 0.5 };
        this.cameraOffset = { x: 0, y: 0 };
    }
    
    // Screen shake effect
    addScreenShake(intensity, duration) {
        this.screenShake.intensity = Math.max(this.screenShake.intensity, intensity);
        this.screenShake.duration = Math.max(this.screenShake.duration, duration);
    }
    
    updateScreenShake() {
        if (this.screenShake.duration > 0) {
            this.screenShake.duration--;
            const shake = this.screenShake.intensity * (this.screenShake.duration / 30);
            this.cameraOffset.x = (Math.random() - 0.5) * shake;
            this.cameraOffset.y = (Math.random() - 0.5) * shake;
            
            if (this.screenShake.duration <= 0) {
                this.cameraOffset.x = 0;
                this.cameraOffset.y = 0;
            }
        }
    }
    
    // Flash effect
    addFlash(color = '#ffffff', intensity = 0.8, duration = 10) {
        this.flashEffect.color = color;
        this.flashEffect.opacity = intensity;
        this.flashEffect.duration = duration;
    }
    
    updateFlash() {
        if (this.flashEffect.opacity > 0) {
            this.flashEffect.opacity -= 0.05;
            if (this.flashEffect.opacity < 0) {
                this.flashEffect.opacity = 0;
            }
        }
    }
    
    drawFlash(ctx) {
        if (this.flashEffect.opacity > 0) {
            ctx.save();
            ctx.fillStyle = this.flashEffect.color;
            ctx.globalAlpha = this.flashEffect.opacity;
            ctx.fillRect(0, 0, CONFIG.CANVAS.WIDTH, CONFIG.CANVAS.HEIGHT);
            ctx.restore();
        }
    }
    
    // Slow motion effect
    activateSlowMotion(duration = 60, factor = 0.3) {
        this.slowMotion.active = true;
        this.slowMotion.duration = duration;
        this.slowMotion.factor = factor;
    }
    
    updateSlowMotion() {
        if (this.slowMotion.active) {
            this.slowMotion.duration--;
            if (this.slowMotion.duration <= 0) {
                this.slowMotion.active = false;
            }
        }
    }
    
    getTimeMultiplier() {
        return this.slowMotion.active ? this.slowMotion.factor : 1.0;
    }
    
    // Update all effects
    update() {
        this.updateScreenShake();
        this.updateFlash();
        this.updateSlowMotion();
    }
    
    // Apply camera transformations
    applyCameraTransform(ctx) {
        ctx.translate(this.cameraOffset.x, this.cameraOffset.y);
    }
    
    resetCameraTransform(ctx) {
        ctx.translate(-this.cameraOffset.x, -this.cameraOffset.y);
    }
}

// Background Manager
class BackgroundManager {
    constructor() {
        this.roadLines = [];
        this.backgroundElements = [];
        this.initializeRoadLines();
        this.initializeBackground();
    }
    
    initializeRoadLines() {
        for (let i = 0; i < 15; i++) {
            this.roadLines.push({
                x: CONFIG.CANVAS.WIDTH / 2 - 2,
                y: i * 50,
                width: 4,
                height: 25
            });
        }
    }
    
    initializeBackground() {
        // Trees
        for (let i = 0; i < 20; i++) {
            this.backgroundElements.push({
                type: 'tree',
                x: Math.random() < 0.5 ? -30 - Math.random() * 50 : CONFIG.CANVAS.WIDTH + 30 + Math.random() * 50,
                y: Math.random() * CONFIG.CANVAS.HEIGHT * 2,
                size: 20 + Math.random() * 30,
                color: '#2d5016'
            });
        }
        
        // Buildings
        for (let i = 0; i < 10; i++) {
            this.backgroundElements.push({
                type: 'building',
                x: Math.random() < 0.5 ? -100 - Math.random() * 100 : CONFIG.CANVAS.WIDTH + 100 + Math.random() * 100,
                y: Math.random() * CONFIG.CANVAS.HEIGHT,
                width: 40 + Math.random() * 60,
                height: 80 + Math.random() * 120,
                color: '#34495e'
            });
        }
        
        // Clouds
        for (let i = 0; i < 8; i++) {
            this.backgroundElements.push({
                type: 'cloud',
                x: Math.random() * CONFIG.CANVAS.WIDTH * 2,
                y: 20 + Math.random() * 100,
                size: 30 + Math.random() * 40,
                speed: 0.2 + Math.random() * 0.3
            });
        }
    }
    
    update(gameSpeed) {
        // Update road lines
        this.roadLines.forEach(line => {
            line.y += gameSpeed;
            if (line.y > CONFIG.CANVAS.HEIGHT) {
                line.y = -50;
            }
        });
        
        // Update background elements
        this.backgroundElements.forEach(element => {
            switch (element.type) {
                case 'tree':
                case 'building':
                    element.y += gameSpeed * 0.3; // Parallax effect
                    if (element.y > CONFIG.CANVAS.HEIGHT + 100) {
                        element.y = -100 - Math.random() * 200;
                    }
                    break;
                case 'cloud':
                    element.x -= element.speed;
                    if (element.x < -element.size) {
                        element.x = CONFIG.CANVAS.WIDTH + element.size;
                    }
                    break;
            }
        });
    }
    
    draw(ctx) {
        this.drawSky(ctx);
        this.drawBackgroundElements(ctx);
        this.drawRoad(ctx);
        this.drawRoadLines(ctx);
    }
    
    drawSky(ctx) {
        // Gradient sky
        const gradient = ctx.createLinearGradient(0, 0, 0, CONFIG.CANVAS.HEIGHT);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(1, '#98D8E8');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, CONFIG.CANVAS.WIDTH, CONFIG.CANVAS.HEIGHT);
    }
    
    drawBackgroundElements(ctx) {
        this.backgroundElements.forEach(element => {
            ctx.save();
            ctx.globalAlpha = 0.6;
            
            switch (element.type) {
                case 'tree':
                    this.drawTree(ctx, element);
                    break;
                case 'building':
                    this.drawBuilding(ctx, element);
                    break;
                case 'cloud':
                    this.drawCloud(ctx, element);
                    break;
            }
            
            ctx.restore();
        });
    }
    
    drawTree(ctx, tree) {
        // Tree trunk
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(tree.x - 3, tree.y, 6, tree.size);
        
        // Tree foliage
        ctx.fillStyle = tree.color;
        ctx.beginPath();
        ctx.arc(tree.x, tree.y - tree.size * 0.3, tree.size * 0.6, 0, Math.PI * 2);
        ctx.fill();
    }
    
    drawBuilding(ctx, building) {
        ctx.fillStyle = building.color;
        ctx.fillRect(building.x, building.y, building.width, building.height);
        
        // Windows
        ctx.fillStyle = '#f1c40f';
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < Math.floor(building.height / 20); j++) {
                if (Math.random() > 0.7) {
                    ctx.fillRect(
                        building.x + 5 + i * 12,
                        building.y + 5 + j * 20,
                        8, 12
                    );
                }
            }
        }
    }
    
    drawCloud(ctx, cloud) {
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        
        // Draw multiple circles to form a cloud
        for (let i = 0; i < 4; i++) {
            const offsetX = (i - 1.5) * cloud.size * 0.3;
            const offsetY = Math.sin(i) * cloud.size * 0.1;
            const radius = cloud.size * (0.3 + Math.random() * 0.2);
            
            ctx.beginPath();
            ctx.arc(cloud.x + offsetX, cloud.y + offsetY, radius, 0, Math.PI * 2);
            ctx.fill();
        }
    }
    
    drawRoad(ctx) {
        // Road surface
        ctx.fillStyle = '#404040';
        ctx.fillRect(0, 0, CONFIG.CANVAS.WIDTH, CONFIG.CANVAS.HEIGHT);
        
        // Road edges
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(20, 0, 4, CONFIG.CANVAS.HEIGHT);
        ctx.fillRect(CONFIG.CANVAS.WIDTH - 24, 0, 4, CONFIG.CANVAS.HEIGHT);
        
        // Lane dividers
        ctx.fillStyle = '#666666';
        CONFIG.CANVAS.LANES.forEach((lane, index) => {
            if (index > 0) {
                ctx.fillRect(lane - CONFIG.CANVAS.LANE_WIDTH / 2, 0, 2, CONFIG.CANVAS.HEIGHT);
            }
        });
    }
    
    drawRoadLines(ctx) {
        ctx.fillStyle = '#ffff00';
        this.roadLines.forEach(line => {
            ctx.fillRect(line.x, line.y, line.width, line.height);
        });
    }
}

// Lighting Effects
class LightingManager {
    constructor() {
        this.headlights = true;
        this.streetLights = [];
        this.initializeStreetLights();
    }
    
    initializeStreetLights() {
        for (let i = 0; i < 10; i++) {
            this.streetLights.push({
                x: Math.random() < 0.5 ? 10 : CONFIG.CANVAS.WIDTH - 10,
                y: i * 80,
                intensity: 0.3 + Math.random() * 0.4
            });
        }
    }
    
    update(gameSpeed) {
        this.streetLights.forEach(light => {
            light.y += gameSpeed;
            if (light.y > CONFIG.CANVAS.HEIGHT) {
                light.y = -80;
                light.x = Math.random() < 0.5 ? 10 : CONFIG.CANVAS.WIDTH - 10;
                light.intensity = 0.3 + Math.random() * 0.4;
            }
        });
    }
    
    drawHeadlights(ctx, playerX, playerY) {
        if (!this.headlights) return;
        
        ctx.save();
        
        // Create headlight gradient
        const gradient = ctx.createRadialGradient(
            playerX + 20, playerY - 20, 0,
            playerX + 20, playerY - 100, 80
        );
        gradient.addColorStop(0, 'rgba(255, 255, 200, 0.3)');
        gradient.addColorStop(1, 'rgba(255, 255, 200, 0)');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, CONFIG.CANVAS.WIDTH, CONFIG.CANVAS.HEIGHT);
        
        ctx.restore();
    }
    
    drawStreetLights(ctx) {
        this.streetLights.forEach(light => {
            ctx.save();
            
            // Light pole
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(light.x - 2, light.y, 4, 30);
            
            // Light glow
            const gradient = ctx.createRadialGradient(
                light.x, light.y, 0,
                light.x, light.y, 40
            );
            gradient.addColorStop(0, `rgba(255, 255, 150, ${light.intensity})`);
            gradient.addColorStop(1, 'rgba(255, 255, 150, 0)');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(light.x, light.y, 40, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        });
    }
}
