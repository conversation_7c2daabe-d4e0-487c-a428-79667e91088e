// Collectible Items
class Collectible {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.collected = false;
        this.animationTimer = 0;
        this.bobOffset = 0;
        this.rotationAngle = 0;
        this.glowIntensity = 0;
        
        this.setupByType();
    }
    
    setupByType() {
        if (this.type.toUpperCase() in CONFIG.COLLECTIBLES) {
            const config = CONFIG.COLLECTIBLES[this.type.toUpperCase()];
            this.width = config.WIDTH;
            this.height = config.HEIGHT;
            this.color = config.COLOR;
        } else if (this.type.toUpperCase() in CONFIG.POWER_UPS) {
            const config = CONFIG.POWER_UPS[this.type.toUpperCase()];
            this.width = config.WIDTH;
            this.height = config.HEIGHT;
            this.color = config.COLOR;
            this.duration = config.DURATION;
        }
        this.value = this.getValueByType();
    }
    
    getValueByType() {
        switch (this.type) {
            case 'coin': return CONFIG.GAME.SCORE_PER_COIN;
            case 'fuel': return CONFIG.GAME.FUEL_REFILL;
            case 'nitro': return 25;
            case 'health': return 25;
            case 'shield': return CONFIG.POWER_UPS.SHIELD.DURATION;
            case 'magnet': return CONFIG.POWER_UPS.MAGNET.DURATION;
            case 'double_score': return CONFIG.POWER_UPS.DOUBLE_SCORE.DURATION;
            case 'slow_motion': return CONFIG.POWER_UPS.SLOW_MOTION.DURATION;
            default: return 0;
        }
    }
    
    update(gameSpeed, particles) {
        this.y += gameSpeed;
        this.animationTimer++;
        
        // Floating animation
        this.bobOffset = Math.sin(this.animationTimer * 0.1) * 2;
        this.rotationAngle += 2;
        this.glowIntensity = 0.5 + Math.sin(this.animationTimer * 0.15) * 0.3;
        
        // Sparkle effect for coins
        if (this.type === 'coin' && this.animationTimer % 20 === 0) {
            particles.createCoinSparkle(
                this.x + this.width / 2,
                this.y + this.height / 2
            );
        }
        
        return this.y < CONFIG.CANVAS.HEIGHT + 50;
    }
    
    collect(player, effects, particles) {
        if (this.collected) return false;
        
        this.collected = true;
        
        // Apply effect based on type
        switch (this.type) {
            case 'coin':
                this.collectCoin(player, effects, particles);
                break;
            case 'fuel':
                this.collectFuel(player, effects, particles);
                break;
            case 'nitro':
                this.collectNitro(player, effects, particles);
                break;
            case 'health':
                this.collectHealth(player, effects, particles);
                break;
            case 'shield':
                this.collectShield(player, effects, particles);
                break;
            case 'magnet':
                this.collectMagnet(player, effects, particles);
                break;
            case 'double_score':
                this.collectDoubleScore(player, effects, particles);
                break;
            case 'slow_motion':
                this.collectSlowMotion(player, effects, particles);
                break;
        }

        return true;
    }
    
    collectCoin(player, effects, particles) {
        // Visual effects
        particles.createCoinSparkle(
            this.x + this.width / 2,
            this.y + this.height / 2
        );
        effects.addFlash('#f1c40f', 0.2, 8);
        
        // Audio
        if (window.audioManager) {
            window.audioManager.playSound('coin');
        }
    }
    
    collectFuel(player, effects, particles) {
        player.addFuel(this.value);
        
        // Visual effects
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 8,
                LIFE: 20,
                SPEED: 3,
                COLORS: ['#e74c3c', '#c0392b']
            }
        );
        effects.addFlash('#e74c3c', 0.2, 8);
    }
    
    collectNitro(player, effects, particles) {
        player.addNitro(this.value);
        
        // Visual effects
        particles.createNitroFlame(
            this.x + this.width / 2,
            this.y + this.height / 2
        );
        effects.addFlash('#3498db', 0.2, 8);
    }
    
    collectHealth(player, effects, particles) {
        player.addHealth(this.value);

        // Visual effects
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 6,
                LIFE: 25,
                SPEED: 2,
                COLORS: ['#2ecc71', '#27ae60']
            }
        );
        effects.addFlash('#2ecc71', 0.2, 8);
    }

    collectShield(player, effects, particles) {
        player.activateShield(this.duration);

        // Visual effects
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 12,
                LIFE: 30,
                SPEED: 3,
                COLORS: ['#00ffff', '#40e0d0', '#00ced1']
            }
        );
        effects.addFlash('#00ffff', 0.3, 12);
    }

    collectMagnet(player, effects, particles) {
        player.activateMagnet(this.duration);

        // Visual effects
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 10,
                LIFE: 25,
                SPEED: 4,
                COLORS: ['#ff00ff', '#da70d6', '#ba55d3']
            }
        );
        effects.addFlash('#ff00ff', 0.3, 10);
    }

    collectDoubleScore(player, effects, particles) {
        player.activateDoubleScore(this.duration);

        // Visual effects
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 15,
                LIFE: 35,
                SPEED: 2,
                COLORS: ['#ffff00', '#ffd700', '#ffb347']
            }
        );
        effects.addFlash('#ffff00', 0.4, 15);
    }

    collectSlowMotion(player, effects, particles) {
        effects.activateSlowMotion(this.duration, 0.3);

        // Visual effects
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 8,
                LIFE: 40,
                SPEED: 1,
                COLORS: ['#00ff00', '#32cd32', '#90ee90']
            }
        );
        effects.addFlash('#00ff00', 0.3, 12);
    }
    
    draw(ctx) {
        if (this.collected) return;
        
        ctx.save();
        
        // Apply transformations
        ctx.translate(this.x + this.width / 2, this.y + this.height / 2 + this.bobOffset);
        ctx.rotate(this.rotationAngle * Math.PI / 180);
        
        // Glow effect
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 10 * this.glowIntensity;
        
        switch (this.type) {
            case 'coin':
                this.drawCoin(ctx);
                break;
            case 'fuel':
                this.drawFuel(ctx);
                break;
            case 'nitro':
                this.drawNitro(ctx);
                break;
            case 'health':
                this.drawHealth(ctx);
                break;
            case 'shield':
                this.drawShield(ctx);
                break;
            case 'magnet':
                this.drawMagnet(ctx);
                break;
            case 'double_score':
                this.drawDoubleScore(ctx);
                break;
            case 'slow_motion':
                this.drawSlowMotion(ctx);
                break;
        }

        ctx.restore();
    }
    
    drawCoin(ctx) {
        // Coin body
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Coin details
        ctx.fillStyle = '#f39c12';
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2 - 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Dollar sign
        ctx.fillStyle = '#f1c40f';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('$', 0, 4);
    }
    
    drawFuel(ctx) {
        // Fuel can body
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
        
        // Fuel can details
        ctx.fillStyle = '#c0392b';
        ctx.fillRect(-this.width / 2 + 2, -this.height / 2 + 2, this.width - 4, this.height - 4);
        
        // Spout
        ctx.fillStyle = '#34495e';
        ctx.fillRect(-this.width / 2 + 5, -this.height / 2 - 3, 6, 3);
        
        // Label
        ctx.fillStyle = '#ffffff';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('FUEL', 0, 2);
    }
    
    drawNitro(ctx) {
        // Nitro bottle body
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
        
        // Nitro bottle details
        ctx.fillStyle = '#2980b9';
        ctx.fillRect(-this.width / 2 + 2, -this.height / 2 + 2, this.width - 4, this.height - 4);
        
        // Cap
        ctx.fillStyle = '#34495e';
        ctx.fillRect(-this.width / 2 + 3, -this.height / 2 - 2, this.width - 6, 2);
        
        // Lightning symbol
        ctx.fillStyle = '#ffffff';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('⚡', 0, 3);
    }
    
    drawHealth(ctx) {
        // Health pack body
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);

        // Red cross
        ctx.fillStyle = this.color;
        // Vertical bar
        ctx.fillRect(-2, -this.height / 2 + 3, 4, this.height - 6);
        // Horizontal bar
        ctx.fillRect(-this.width / 2 + 3, -2, this.width - 6, 4);
    }

    drawShield(ctx) {
        // Shield body
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.moveTo(0, -this.height / 2);
        ctx.lineTo(this.width / 3, -this.height / 4);
        ctx.lineTo(this.width / 3, this.height / 4);
        ctx.lineTo(0, this.height / 2);
        ctx.lineTo(-this.width / 3, this.height / 4);
        ctx.lineTo(-this.width / 3, -this.height / 4);
        ctx.closePath();
        ctx.fill();

        // Shield glow
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.stroke();
    }

    drawMagnet(ctx) {
        // Magnet body
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);

        // Magnet poles
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width / 3, this.height);
        ctx.fillStyle = '#0000ff';
        ctx.fillRect(this.width / 6, -this.height / 2, this.width / 3, this.height);

        // Magnet symbol
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('M', 0, 3);
    }

    drawDoubleScore(ctx) {
        // Star shape for double score
        ctx.fillStyle = this.color;
        ctx.beginPath();
        const spikes = 5;
        const outerRadius = this.width / 2;
        const innerRadius = outerRadius * 0.5;

        for (let i = 0; i < spikes * 2; i++) {
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const angle = (i * Math.PI) / spikes;
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;

            if (i === 0) ctx.moveTo(x, y);
            else ctx.lineTo(x, y);
        }
        ctx.closePath();
        ctx.fill();

        // 2x text
        ctx.fillStyle = '#000000';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('2X', 0, 2);
    }

    drawSlowMotion(ctx) {
        // Clock shape
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();

        // Clock face
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2 - 2, 0, Math.PI * 2);
        ctx.fill();

        // Clock hands
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(0, -this.width / 3);
        ctx.moveTo(0, 0);
        ctx.lineTo(this.width / 4, 0);
        ctx.stroke();

        // Slow motion effect lines
        ctx.strokeStyle = this.color;
        ctx.lineWidth = 1;
        for (let i = 0; i < 4; i++) {
            const angle = (i * Math.PI) / 2;
            const x1 = Math.cos(angle) * (this.width / 2 + 3);
            const y1 = Math.sin(angle) * (this.width / 2 + 3);
            const x2 = Math.cos(angle) * (this.width / 2 + 6);
            const y2 = Math.sin(angle) * (this.width / 2 + 6);

            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// Collectibles Manager
class CollectiblesManager {
    constructor() {
        this.collectibles = [];
        this.spawnTimer = 0;
        this.spawnInterval = 120;
    }
    
    update(gameSpeed, particles) {
        // Update existing collectibles
        this.collectibles = this.collectibles.filter(collectible => 
            collectible.update(gameSpeed, particles)
        );
        
        // Spawn new collectibles
        this.spawnTimer++;
        if (this.spawnTimer >= this.spawnInterval) {
            this.spawnCollectible();
            this.spawnTimer = 0;
            this.spawnInterval = 80 + Math.random() * 120;
        }
    }
    
    spawnCollectible() {
        const lanes = CONFIG.CANVAS.LANES;
        const lane = lanes[Math.floor(Math.random() * lanes.length)];

        // Determine collectible type based on probability
        const rand = Math.random();
        let type = 'coin';
        let config;

        // Check for power-ups first (rarer)
        if (rand < CONFIG.POWER_UPS.SHIELD.SPAWN_CHANCE) {
            type = 'shield';
            config = CONFIG.POWER_UPS.SHIELD;
        } else if (rand < CONFIG.POWER_UPS.SHIELD.SPAWN_CHANCE + CONFIG.POWER_UPS.MAGNET.SPAWN_CHANCE) {
            type = 'magnet';
            config = CONFIG.POWER_UPS.MAGNET;
        } else if (rand < CONFIG.POWER_UPS.SHIELD.SPAWN_CHANCE + CONFIG.POWER_UPS.MAGNET.SPAWN_CHANCE + CONFIG.POWER_UPS.DOUBLE_SCORE.SPAWN_CHANCE) {
            type = 'double_score';
            config = CONFIG.POWER_UPS.DOUBLE_SCORE;
        } else if (rand < CONFIG.POWER_UPS.SHIELD.SPAWN_CHANCE + CONFIG.POWER_UPS.MAGNET.SPAWN_CHANCE + CONFIG.POWER_UPS.DOUBLE_SCORE.SPAWN_CHANCE + CONFIG.POWER_UPS.SLOW_MOTION.SPAWN_CHANCE) {
            type = 'slow_motion';
            config = CONFIG.POWER_UPS.SLOW_MOTION;
        }
        // Regular collectibles
        else if (rand < 0.4) {
            type = 'coin';
            config = CONFIG.COLLECTIBLES.COIN;
        } else if (rand < 0.5) {
            type = 'fuel';
            config = CONFIG.COLLECTIBLES.FUEL;
        } else if (rand < 0.6) {
            type = 'nitro';
            config = CONFIG.COLLECTIBLES.NITRO;
        } else if (rand < 0.65) {
            type = 'health';
            config = CONFIG.COLLECTIBLES.HEALTH;
        } else {
            type = 'coin'; // Default fallback
            config = CONFIG.COLLECTIBLES.COIN;
        }

        const collectible = new Collectible(
            lane - config.WIDTH / 2,
            -config.HEIGHT,
            type
        );

        this.collectibles.push(collectible);
    }
    
    checkCollisions(player, effects, particles) {
        let score = 0;

        for (let i = this.collectibles.length - 1; i >= 0; i--) {
            const collectible = this.collectibles[i];

            // Magnet effect
            if (player.magnetActive && collectible.type === 'coin') {
                const distance = this.getDistance(player.getCenter(), {
                    x: collectible.x + collectible.width / 2,
                    y: collectible.y + collectible.height / 2
                });

                if (distance < CONFIG.POWER_UPS.MAGNET.RANGE) {
                    // Pull collectible towards player
                    const dx = (player.x + player.width / 2) - (collectible.x + collectible.width / 2);
                    const dy = (player.y + player.height / 2) - (collectible.y + collectible.height / 2);
                    const pullStrength = 0.3;

                    collectible.x += dx * pullStrength;
                    collectible.y += dy * pullStrength;
                }
            }

            if (this.isColliding(player.getBounds(), collectible.getBounds())) {
                if (collectible.collect(player, effects, particles)) {
                    if (collectible.type === 'coin') {
                        score += collectible.value * player.getScoreMultiplier();
                        player.addCombo();
                    }
                    this.collectibles.splice(i, 1);
                }
            }
        }

        return score;
    }

    getDistance(point1, point2) {
        const dx = point1.x - point2.x;
        const dy = point1.y - point2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    isColliding(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }
    
    draw(ctx) {
        this.collectibles.forEach(collectible => collectible.draw(ctx));
    }
    
    clear() {
        this.collectibles = [];
    }
}
