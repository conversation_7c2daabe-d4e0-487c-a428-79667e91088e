// Collectible Items
class Collectible {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.collected = false;
        this.animationTimer = 0;
        this.bobOffset = 0;
        this.rotationAngle = 0;
        this.glowIntensity = 0;
        
        this.setupByType();
    }
    
    setupByType() {
        const config = CONFIG.COLLECTIBLES[this.type.toUpperCase()];
        this.width = config.WIDTH;
        this.height = config.HEIGHT;
        this.color = config.COLOR;
        this.value = this.getValueByType();
    }
    
    getValueByType() {
        switch (this.type) {
            case 'coin': return CONFIG.GAME.SCORE_PER_COIN;
            case 'fuel': return CONFIG.GAME.FUEL_REFILL;
            case 'nitro': return 25;
            case 'health': return 25;
            default: return 0;
        }
    }
    
    update(gameSpeed, particles) {
        this.y += gameSpeed;
        this.animationTimer++;
        
        // Floating animation
        this.bobOffset = Math.sin(this.animationTimer * 0.1) * 2;
        this.rotationAngle += 2;
        this.glowIntensity = 0.5 + Math.sin(this.animationTimer * 0.15) * 0.3;
        
        // Sparkle effect for coins
        if (this.type === 'coin' && this.animationTimer % 20 === 0) {
            particles.createCoinSparkle(
                this.x + this.width / 2,
                this.y + this.height / 2
            );
        }
        
        return this.y < CONFIG.CANVAS.HEIGHT + 50;
    }
    
    collect(player, effects, particles) {
        if (this.collected) return false;
        
        this.collected = true;
        
        // Apply effect based on type
        switch (this.type) {
            case 'coin':
                this.collectCoin(player, effects, particles);
                break;
            case 'fuel':
                this.collectFuel(player, effects, particles);
                break;
            case 'nitro':
                this.collectNitro(player, effects, particles);
                break;
            case 'health':
                this.collectHealth(player, effects, particles);
                break;
        }
        
        return true;
    }
    
    collectCoin(player, effects, particles) {
        // Visual effects
        particles.createCoinSparkle(
            this.x + this.width / 2,
            this.y + this.height / 2
        );
        effects.addFlash('#f1c40f', 0.2, 8);
        
        // Audio
        if (window.audioManager) {
            window.audioManager.playSound('coin');
        }
    }
    
    collectFuel(player, effects, particles) {
        player.addFuel(this.value);
        
        // Visual effects
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 8,
                LIFE: 20,
                SPEED: 3,
                COLORS: ['#e74c3c', '#c0392b']
            }
        );
        effects.addFlash('#e74c3c', 0.2, 8);
    }
    
    collectNitro(player, effects, particles) {
        player.addNitro(this.value);
        
        // Visual effects
        particles.createNitroFlame(
            this.x + this.width / 2,
            this.y + this.height / 2
        );
        effects.addFlash('#3498db', 0.2, 8);
    }
    
    collectHealth(player, effects, particles) {
        player.addHealth(this.value);
        
        // Visual effects
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 6,
                LIFE: 25,
                SPEED: 2,
                COLORS: ['#2ecc71', '#27ae60']
            }
        );
        effects.addFlash('#2ecc71', 0.2, 8);
    }
    
    draw(ctx) {
        if (this.collected) return;
        
        ctx.save();
        
        // Apply transformations
        ctx.translate(this.x + this.width / 2, this.y + this.height / 2 + this.bobOffset);
        ctx.rotate(this.rotationAngle * Math.PI / 180);
        
        // Glow effect
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 10 * this.glowIntensity;
        
        switch (this.type) {
            case 'coin':
                this.drawCoin(ctx);
                break;
            case 'fuel':
                this.drawFuel(ctx);
                break;
            case 'nitro':
                this.drawNitro(ctx);
                break;
            case 'health':
                this.drawHealth(ctx);
                break;
        }
        
        ctx.restore();
    }
    
    drawCoin(ctx) {
        // Coin body
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Coin details
        ctx.fillStyle = '#f39c12';
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2 - 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Dollar sign
        ctx.fillStyle = '#f1c40f';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('$', 0, 4);
    }
    
    drawFuel(ctx) {
        // Fuel can body
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
        
        // Fuel can details
        ctx.fillStyle = '#c0392b';
        ctx.fillRect(-this.width / 2 + 2, -this.height / 2 + 2, this.width - 4, this.height - 4);
        
        // Spout
        ctx.fillStyle = '#34495e';
        ctx.fillRect(-this.width / 2 + 5, -this.height / 2 - 3, 6, 3);
        
        // Label
        ctx.fillStyle = '#ffffff';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('FUEL', 0, 2);
    }
    
    drawNitro(ctx) {
        // Nitro bottle body
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
        
        // Nitro bottle details
        ctx.fillStyle = '#2980b9';
        ctx.fillRect(-this.width / 2 + 2, -this.height / 2 + 2, this.width - 4, this.height - 4);
        
        // Cap
        ctx.fillStyle = '#34495e';
        ctx.fillRect(-this.width / 2 + 3, -this.height / 2 - 2, this.width - 6, 2);
        
        // Lightning symbol
        ctx.fillStyle = '#ffffff';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('⚡', 0, 3);
    }
    
    drawHealth(ctx) {
        // Health pack body
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
        
        // Red cross
        ctx.fillStyle = this.color;
        // Vertical bar
        ctx.fillRect(-2, -this.height / 2 + 3, 4, this.height - 6);
        // Horizontal bar
        ctx.fillRect(-this.width / 2 + 3, -2, this.width - 6, 4);
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// Collectibles Manager
class CollectiblesManager {
    constructor() {
        this.collectibles = [];
        this.spawnTimer = 0;
        this.spawnInterval = 120;
    }
    
    update(gameSpeed, particles) {
        // Update existing collectibles
        this.collectibles = this.collectibles.filter(collectible => 
            collectible.update(gameSpeed, particles)
        );
        
        // Spawn new collectibles
        this.spawnTimer++;
        if (this.spawnTimer >= this.spawnInterval) {
            this.spawnCollectible();
            this.spawnTimer = 0;
            this.spawnInterval = 80 + Math.random() * 120;
        }
    }
    
    spawnCollectible() {
        const lanes = CONFIG.CANVAS.LANES;
        const lane = lanes[Math.floor(Math.random() * lanes.length)];
        
        // Determine collectible type based on probability
        const rand = Math.random();
        let type = 'coin';
        
        if (rand < CONFIG.COLLECTIBLES.COIN.SPAWN_CHANCE) {
            type = 'coin';
        } else if (rand < CONFIG.COLLECTIBLES.COIN.SPAWN_CHANCE + CONFIG.COLLECTIBLES.FUEL.SPAWN_CHANCE) {
            type = 'fuel';
        } else if (rand < CONFIG.COLLECTIBLES.COIN.SPAWN_CHANCE + CONFIG.COLLECTIBLES.FUEL.SPAWN_CHANCE + CONFIG.COLLECTIBLES.NITRO.SPAWN_CHANCE) {
            type = 'nitro';
        } else if (rand < CONFIG.COLLECTIBLES.COIN.SPAWN_CHANCE + CONFIG.COLLECTIBLES.FUEL.SPAWN_CHANCE + CONFIG.COLLECTIBLES.NITRO.SPAWN_CHANCE + CONFIG.COLLECTIBLES.HEALTH.SPAWN_CHANCE) {
            type = 'health';
        }
        
        const collectible = new Collectible(
            lane - CONFIG.COLLECTIBLES[type.toUpperCase()].WIDTH / 2,
            -CONFIG.COLLECTIBLES[type.toUpperCase()].HEIGHT,
            type
        );
        
        this.collectibles.push(collectible);
    }
    
    checkCollisions(player, effects, particles) {
        let score = 0;
        
        for (let i = this.collectibles.length - 1; i >= 0; i--) {
            const collectible = this.collectibles[i];
            
            if (this.isColliding(player.getBounds(), collectible.getBounds())) {
                if (collectible.collect(player, effects, particles)) {
                    if (collectible.type === 'coin') {
                        score += collectible.value;
                    }
                    this.collectibles.splice(i, 1);
                }
            }
        }
        
        return score;
    }
    
    isColliding(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }
    
    draw(ctx) {
        this.collectibles.forEach(collectible => collectible.draw(ctx));
    }
    
    clear() {
        this.collectibles = [];
    }
}
