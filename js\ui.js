// UI Manager
class UIManager {
    constructor() {
        this.elements = this.initializeElements();
        this.setupEventListeners();
        this.achievements = [];
        this.notifications = [];
    }
    
    initializeElements() {
        return {
            // Score elements
            score: document.getElementById('score'),
            highScore: document.getElementById('highScore'),
            speed: document.getElementById('speed'),
            level: document.getElementById('level'),
            
            // HUD elements
            fuelBar: document.getElementById('fuelBar'),
            fuelValue: document.getElementById('fuelValue'),
            nitroBar: document.getElementById('nitroBar'),
            nitroValue: document.getElementById('nitroValue'),
            healthBar: document.getElementById('healthBar'),
            healthValue: document.getElementById('healthValue'),
            
            // Screen elements
            gameOverlay: document.getElementById('gameOverlay'),
            startScreen: document.getElementById('startScreen'),
            gameOverScreen: document.getElementById('gameOverScreen'),
            pauseScreen: document.getElementById('pauseScreen'),
            
            // Buttons
            startBtn: document.getElementById('startBtn'),
            restartBtn: document.getElementById('restartBtn'),
            shareBtn: document.getElementById('shareBtn'),
            soundToggle: document.getElementById('soundToggle'),
            musicToggle: document.getElementById('musicToggle'),
            
            // Final stats
            finalScore: document.getElementById('finalScore'),
            finalLevel: document.getElementById('finalLevel'),
            finalDistance: document.getElementById('finalDistance'),
            finalCoins: document.getElementById('finalCoins'),
            finalTime: document.getElementById('finalTime'),
            achievements: document.getElementById('achievements'),
            
            // Mini map
            miniMap: document.getElementById('miniMap'),
            miniMapCanvas: document.getElementById('miniMapCanvas')
        };
    }
    
    setupEventListeners() {
        // Difficulty selection
        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.difficulty-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                window.selectedDifficulty = e.target.dataset.level;
            });
        });
        
        // Sound controls
        this.elements.soundToggle?.addEventListener('click', () => {
            if (window.audioManager) {
                const enabled = window.audioManager.toggleSound();
                this.elements.soundToggle.textContent = enabled ? '🔊' : '🔇';
            }
        });
        
        this.elements.musicToggle?.addEventListener('click', () => {
            if (window.audioManager) {
                const enabled = window.audioManager.toggleMusic();
                this.elements.musicToggle.textContent = enabled ? '🎵' : '🎶';
            }
        });
        
        // Share button
        this.elements.shareBtn?.addEventListener('click', () => {
            this.shareScore();
        });
    }
    
    updateScore(score) {
        if (this.elements.score) {
            this.elements.score.textContent = score;
            this.elements.score.classList.add('score-update');
            setTimeout(() => this.elements.score.classList.remove('score-update'), 300);
        }
    }
    
    updateHighScore(highScore) {
        if (this.elements.highScore) {
            this.elements.highScore.textContent = highScore;
        }
    }
    
    updateSpeed(speed) {
        if (this.elements.speed) {
            this.elements.speed.textContent = Math.floor(speed);
        }
    }
    
    updateLevel(level) {
        if (this.elements.level) {
            this.elements.level.textContent = level;
        }
    }
    
    updateFuel(fuel) {
        if (this.elements.fuelBar && this.elements.fuelValue) {
            const percentage = Math.max(0, fuel);
            this.elements.fuelBar.style.width = `${percentage}%`;
            this.elements.fuelValue.textContent = `${Math.floor(percentage)}%`;
            
            // Change color based on fuel level
            if (percentage < 20) {
                this.elements.fuelBar.style.backgroundColor = '#e74c3c';
            } else if (percentage < 50) {
                this.elements.fuelBar.style.backgroundColor = '#f39c12';
            } else {
                this.elements.fuelBar.style.backgroundColor = '#2ecc71';
            }
        }
    }
    
    updateNitro(nitro) {
        if (this.elements.nitroBar && this.elements.nitroValue) {
            const percentage = Math.max(0, nitro);
            this.elements.nitroBar.style.width = `${percentage}%`;
            this.elements.nitroValue.textContent = `${Math.floor(percentage)}%`;
        }
    }
    
    updateHealth(health) {
        if (this.elements.healthBar && this.elements.healthValue) {
            const percentage = Math.max(0, health);
            this.elements.healthBar.style.width = `${percentage}%`;
            this.elements.healthValue.textContent = `${Math.floor(percentage)}%`;
            
            // Change color based on health level
            if (percentage < 25) {
                this.elements.healthBar.style.backgroundColor = '#e74c3c';
            } else if (percentage < 50) {
                this.elements.healthBar.style.backgroundColor = '#f39c12';
            } else {
                this.elements.healthBar.style.backgroundColor = '#2ecc71';
            }
        }
    }
    
    showScreen(screenName) {
        // Hide all screens
        this.elements.startScreen.style.display = 'none';
        this.elements.gameOverScreen.style.display = 'none';
        this.elements.pauseScreen.style.display = 'none';
        
        // Show requested screen
        switch (screenName) {
            case 'start':
                this.elements.startScreen.style.display = 'block';
                this.elements.gameOverlay.style.display = 'flex';
                break;
            case 'gameOver':
                this.elements.gameOverScreen.style.display = 'block';
                this.elements.gameOverlay.style.display = 'flex';
                break;
            case 'pause':
                this.elements.pauseScreen.style.display = 'block';
                this.elements.gameOverlay.style.display = 'flex';
                break;
            case 'none':
                this.elements.gameOverlay.style.display = 'none';
                break;
        }
    }
    
    updateFinalStats(stats) {
        if (this.elements.finalScore) this.elements.finalScore.textContent = stats.score;
        if (this.elements.finalLevel) this.elements.finalLevel.textContent = stats.level;
        if (this.elements.finalDistance) this.elements.finalDistance.textContent = stats.distance;
        if (this.elements.finalCoins) this.elements.finalCoins.textContent = stats.coins;
        if (this.elements.finalTime) this.elements.finalTime.textContent = stats.time;
        
        this.updateAchievements(stats);
    }
    
    updateAchievements(stats) {
        this.achievements = [];
        
        // Check for achievements
        if (stats.score >= 1000) this.achievements.push('🏆 أول ألف نقطة!');
        if (stats.score >= 5000) this.achievements.push('🌟 خمسة آلاف نقطة!');
        if (stats.level >= 5) this.achievements.push('🚀 وصلت للمستوى الخامس!');
        if (stats.coins >= 50) this.achievements.push('💰 جامع العملات!');
        if (stats.distance >= 10000) this.achievements.push('🛣️ رحلة طويلة!');
        if (stats.time >= 300) this.achievements.push('⏰ لاعب صبور!');
        
        // Display achievements
        if (this.elements.achievements) {
            this.elements.achievements.innerHTML = '';
            if (this.achievements.length > 0) {
                const title = document.createElement('h4');
                title.textContent = 'الإنجازات المحققة:';
                this.elements.achievements.appendChild(title);
                
                this.achievements.forEach(achievement => {
                    const div = document.createElement('div');
                    div.className = 'achievement';
                    div.textContent = achievement;
                    this.elements.achievements.appendChild(div);
                });
            }
        }
    }
    
    addNotification(text, type = 'info', duration = 3000) {
        const notification = {
            id: Date.now(),
            text: text,
            type: type,
            duration: duration,
            timer: 0
        };
        
        this.notifications.push(notification);
        
        // Remove after duration
        setTimeout(() => {
            this.notifications = this.notifications.filter(n => n.id !== notification.id);
        }, duration);
    }
    
    updateNotifications() {
        this.notifications.forEach(notification => {
            notification.timer += 16; // Assuming 60 FPS
        });
    }
    
    drawNotifications(ctx) {
        let yOffset = 50;
        
        this.notifications.forEach(notification => {
            const alpha = Math.max(0, 1 - (notification.timer / notification.duration));
            
            ctx.save();
            ctx.globalAlpha = alpha;
            
            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.fillRect(10, yOffset, 200, 30);
            
            // Text
            ctx.fillStyle = '#ffffff';
            ctx.font = '14px Arial';
            ctx.fillText(notification.text, 15, yOffset + 20);
            
            ctx.restore();
            
            yOffset += 35;
        });
    }
    
    updateMiniMap(player, enemies, collectibles) {
        if (!this.elements.miniMapCanvas) return;
        
        const miniCtx = this.elements.miniMapCanvas.getContext('2d');
        const scale = 0.2;
        
        // Clear mini map
        miniCtx.clearRect(0, 0, 80, 120);
        
        // Draw background
        miniCtx.fillStyle = '#404040';
        miniCtx.fillRect(0, 0, 80, 120);
        
        // Draw player
        miniCtx.fillStyle = '#ff4757';
        miniCtx.fillRect(
            player.x * scale,
            player.y * scale,
            player.width * scale,
            player.height * scale
        );
        
        // Draw enemies
        miniCtx.fillStyle = '#3742fa';
        enemies.forEach(enemy => {
            miniCtx.fillRect(
                enemy.x * scale,
                enemy.y * scale,
                enemy.width * scale,
                enemy.height * scale
            );
        });
        
        // Draw collectibles
        miniCtx.fillStyle = '#f1c40f';
        collectibles.forEach(collectible => {
            miniCtx.fillRect(
                collectible.x * scale,
                collectible.y * scale,
                collectible.width * scale,
                collectible.height * scale
            );
        });
    }
    
    shareScore() {
        const score = this.elements.finalScore?.textContent || '0';
        const level = this.elements.finalLevel?.textContent || '1';
        
        const shareText = `لقد حققت ${score} نقطة ووصلت للمستوى ${level} في لعبة السيارات الاحترافية! 🏎️🏆`;
        
        if (navigator.share) {
            navigator.share({
                title: 'لعبة السيارات الاحترافية',
                text: shareText,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareText).then(() => {
                this.addNotification('تم نسخ النتيجة للحافظة!', 'success');
            });
        }
    }
    
    showLevelUpEffect(level) {
        // Create level up overlay
        const overlay = document.createElement('div');
        overlay.className = 'level-up-overlay';
        overlay.innerHTML = `
            <div class="level-up-content">
                <h2>🎉 مستوى جديد!</h2>
                <p>المستوى ${level}</p>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // Remove after animation
        setTimeout(() => {
            document.body.removeChild(overlay);
        }, 3000);
    }
}
