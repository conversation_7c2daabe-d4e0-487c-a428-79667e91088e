// Fullscreen Manager
class FullscreenManager {
    constructor() {
        this.isFullscreen = false;
        this.originalCanvasSize = { width: 400, height: 600 };
        this.fullscreenCanvasSize = { width: 0, height: 0 };
        this.aspectRatio = this.originalCanvasSize.width / this.originalCanvasSize.height;
        this.scaleFactor = 1;
        
        this.setupEventListeners();
        this.createFullscreenButton();
        this.createFullscreenUI();
    }
    
    setupEventListeners() {
        // مراقبة تغييرات الشاشة الكاملة
        document.addEventListener('fullscreenchange', () => {
            this.handleFullscreenChange();
        });
        
        document.addEventListener('webkitfullscreenchange', () => {
            this.handleFullscreenChange();
        });
        
        document.addEventListener('mozfullscreenchange', () => {
            this.handleFullscreenChange();
        });
        
        document.addEventListener('MSFullscreenChange', () => {
            this.handleFullscreenChange();
        });
        
        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', () => {
            if (this.isFullscreen) {
                this.adjustFullscreenLayout();
            }
        });
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F11') {
                e.preventDefault();
                this.toggleFullscreen();
            } else if (e.key === 'Escape' && this.isFullscreen) {
                this.exitFullscreen();
            }
        });
    }
    
    createFullscreenButton() {
        // إنشاء زر الشاشة الكاملة
        const fullscreenBtn = document.createElement('button');
        fullscreenBtn.id = 'fullscreenBtn';
        fullscreenBtn.className = 'fullscreen-btn';
        fullscreenBtn.innerHTML = '⛶';
        fullscreenBtn.title = 'الشاشة الكاملة (F11)';
        
        fullscreenBtn.addEventListener('click', () => {
            this.toggleFullscreen();
        });
        
        // إضافة الزر إلى منطقة التحكم بالصوت
        const soundControls = document.querySelector('.sound-controls');
        if (soundControls) {
            soundControls.appendChild(fullscreenBtn);
        }
    }
    
    createFullscreenUI() {
        // إنشاء واجهة مستخدم للشاشة الكاملة
        const fullscreenUI = document.createElement('div');
        fullscreenUI.id = 'fullscreenUI';
        fullscreenUI.className = 'fullscreen-ui';
        fullscreenUI.style.display = 'none';
        
        fullscreenUI.innerHTML = `
            <div class="fullscreen-controls">
                <button id="exitFullscreenBtn" class="fullscreen-control-btn">
                    ✕ خروج من الشاشة الكاملة
                </button>
                <div class="fullscreen-info">
                    <span id="gamepadIndicator" style="display: none;">🎮 متصل</span>
                    <span class="fullscreen-tip">F11 أو ESC للخروج</span>
                </div>
            </div>
            
            <div class="fullscreen-game-area">
                <!-- سيتم نقل اللعبة هنا في وضع الشاشة الكاملة -->
            </div>
            
            <div class="fullscreen-hud">
                <div class="fullscreen-score-board">
                    <div class="fs-score">النقاط: <span id="fsScore">0</span></div>
                    <div class="fs-level">المستوى: <span id="fsLevel">1</span></div>
                    <div class="fs-speed">السرعة: <span id="fsSpeed">0</span> كم/س</div>
                </div>
                
                <div class="fullscreen-meters">
                    <div class="fs-meter">
                        <div class="fs-meter-label">⛽</div>
                        <div class="fs-meter-bar">
                            <div class="fs-meter-fill" id="fsFuelBar"></div>
                        </div>
                        <div class="fs-meter-value" id="fsFuelValue">100%</div>
                    </div>
                    
                    <div class="fs-meter">
                        <div class="fs-meter-label">🚀</div>
                        <div class="fs-meter-bar">
                            <div class="fs-meter-fill nitro-fill" id="fsNitroBar"></div>
                        </div>
                        <div class="fs-meter-value" id="fsNitroValue">0%</div>
                    </div>
                    
                    <div class="fs-meter">
                        <div class="fs-meter-label">❤️</div>
                        <div class="fs-meter-bar">
                            <div class="fs-meter-fill health-fill" id="fsHealthBar"></div>
                        </div>
                        <div class="fs-meter-value" id="fsHealthValue">100%</div>
                    </div>
                </div>
                
                <div class="fullscreen-gamepad-help" id="gamepadHelp" style="display: none;">
                    <h4>🎮 تحكم الجويستيك:</h4>
                    <div class="gamepad-controls">
                        <div class="gamepad-control">
                            <span class="gamepad-button">العصا اليسرى</span>
                            <span>الحركة</span>
                        </div>
                        <div class="gamepad-control">
                            <span class="gamepad-button">X</span>
                            <span>نيتروس</span>
                        </div>
                        <div class="gamepad-control">
                            <span class="gamepad-button">A</span>
                            <span>تأكيد</span>
                        </div>
                        <div class="gamepad-control">
                            <span class="gamepad-button">Start</span>
                            <span>إيقاف/تشغيل</span>
                        </div>
                        <div class="gamepad-control">
                            <span class="gamepad-button">Y</span>
                            <span>شاشة كاملة</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(fullscreenUI);
        
        // إعداد أحداث الأزرار
        document.getElementById('exitFullscreenBtn').addEventListener('click', () => {
            this.exitFullscreen();
        });
    }
    
    toggleFullscreen() {
        if (this.isFullscreen) {
            this.exitFullscreen();
        } else {
            this.enterFullscreen();
        }
    }
    
    enterFullscreen() {
        const element = document.documentElement;
        
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }
    }
    
    exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }
    
    handleFullscreenChange() {
        this.isFullscreen = !!document.fullscreenElement || 
                           !!document.webkitFullscreenElement || 
                           !!document.mozFullScreenElement || 
                           !!document.msFullscreenElement;
        
        if (this.isFullscreen) {
            this.setupFullscreenMode();
        } else {
            this.setupWindowMode();
        }
        
        // إشعار للمستخدم
        if (window.game && window.game.ui) {
            const message = this.isFullscreen ? 
                '🖥️ تم تفعيل الشاشة الكاملة' : 
                '🪟 تم الخروج من الشاشة الكاملة';
            window.game.ui.addNotification(message, 'info', 2000);
        }
    }
    
    setupFullscreenMode() {
        console.log('🖥️ دخول وضع الشاشة الكاملة');
        
        // إخفاء الواجهة العادية
        const gameContainer = document.querySelector('.game-container');
        if (gameContainer) {
            gameContainer.style.display = 'none';
        }
        
        // إظهار واجهة الشاشة الكاملة
        const fullscreenUI = document.getElementById('fullscreenUI');
        if (fullscreenUI) {
            fullscreenUI.style.display = 'flex';
        }
        
        // نقل Canvas إلى منطقة الشاشة الكاملة
        this.moveCanvasToFullscreen();
        
        // تعديل تخطيط الشاشة الكاملة
        this.adjustFullscreenLayout();
        
        // إظهار مساعدة الجويستيك إذا كان متصلاً
        this.updateGamepadHelp();
        
        // تحديث زر الشاشة الكاملة
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.innerHTML = '⛷';
            fullscreenBtn.title = 'خروج من الشاشة الكاملة (ESC)';
        }
    }
    
    setupWindowMode() {
        console.log('🪟 العودة لوضع النافذة');
        
        // إخفاء واجهة الشاشة الكاملة
        const fullscreenUI = document.getElementById('fullscreenUI');
        if (fullscreenUI) {
            fullscreenUI.style.display = 'none';
        }
        
        // إظهار الواجهة العادية
        const gameContainer = document.querySelector('.game-container');
        if (gameContainer) {
            gameContainer.style.display = 'block';
        }
        
        // إعادة Canvas إلى مكانه الأصلي
        this.moveCanvasToWindow();
        
        // إعادة تعيين حجم Canvas
        this.resetCanvasSize();
        
        // تحديث زر الشاشة الكاملة
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.innerHTML = '⛶';
            fullscreenBtn.title = 'الشاشة الكاملة (F11)';
        }
    }
    
    moveCanvasToFullscreen() {
        const canvas = document.getElementById('gameCanvas');
        const fullscreenGameArea = document.querySelector('.fullscreen-game-area');
        
        if (canvas && fullscreenGameArea) {
            fullscreenGameArea.appendChild(canvas);
        }
    }
    
    moveCanvasToWindow() {
        const canvas = document.getElementById('gameCanvas');
        const originalGameArea = document.querySelector('.game-area');
        
        if (canvas && originalGameArea) {
            // إعادة Canvas قبل overlay
            const overlay = originalGameArea.querySelector('.game-overlay');
            if (overlay) {
                originalGameArea.insertBefore(canvas, overlay);
            } else {
                originalGameArea.appendChild(canvas);
            }
        }
    }
    
    adjustFullscreenLayout() {
        const canvas = document.getElementById('gameCanvas');
        if (!canvas) return;
        
        // حساب الحجم الأمثل للشاشة الكاملة
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        
        // ترك مساحة للواجهة (HUD)
        const availableHeight = screenHeight - 120; // 120px للواجهة العلوية والسفلية
        const availableWidth = screenWidth - 40; // 40px للهوامش
        
        // حساب الحجم مع الحفاظ على النسبة
        let newWidth, newHeight;
        
        if (availableWidth / availableHeight > this.aspectRatio) {
            // الارتفاع هو المحدد
            newHeight = availableHeight;
            newWidth = newHeight * this.aspectRatio;
        } else {
            // العرض هو المحدد
            newWidth = availableWidth;
            newHeight = newWidth / this.aspectRatio;
        }
        
        // تطبيق الحجم الجديد
        canvas.style.width = `${newWidth}px`;
        canvas.style.height = `${newHeight}px`;
        
        // حفظ معامل التكبير
        this.scaleFactor = newWidth / this.originalCanvasSize.width;
        
        // تحديث حجم Canvas الداخلي إذا لزم الأمر
        this.fullscreenCanvasSize = { width: newWidth, height: newHeight };
        
        console.log(`📏 حجم الشاشة الكاملة: ${newWidth}x${newHeight} (معامل التكبير: ${this.scaleFactor.toFixed(2)})`);
    }
    
    resetCanvasSize() {
        const canvas = document.getElementById('gameCanvas');
        if (!canvas) return;
        
        canvas.style.width = `${this.originalCanvasSize.width}px`;
        canvas.style.height = `${this.originalCanvasSize.height}px`;
        this.scaleFactor = 1;
    }
    
    updateGamepadHelp() {
        const gamepadHelp = document.getElementById('gamepadHelp');
        const gamepadManager = window.gamepadManager;
        
        if (gamepadHelp && gamepadManager) {
            gamepadHelp.style.display = gamepadManager.connected ? 'block' : 'none';
        }
    }
    
    // تحديث واجهة الشاشة الكاملة
    updateFullscreenUI(gameData) {
        if (!this.isFullscreen) return;
        
        // تحديث النقاط والمعلومات
        const fsScore = document.getElementById('fsScore');
        const fsLevel = document.getElementById('fsLevel');
        const fsSpeed = document.getElementById('fsSpeed');
        
        if (fsScore) fsScore.textContent = gameData.score || 0;
        if (fsLevel) fsLevel.textContent = gameData.level || 1;
        if (fsSpeed) fsSpeed.textContent = gameData.speed || 0;
        
        // تحديث المؤشرات
        this.updateFullscreenMeter('fsFuelBar', 'fsFuelValue', gameData.fuel || 100);
        this.updateFullscreenMeter('fsNitroBar', 'fsNitroValue', gameData.nitro || 0);
        this.updateFullscreenMeter('fsHealthBar', 'fsHealthValue', gameData.health || 100);
    }
    
    updateFullscreenMeter(barId, valueId, percentage) {
        const bar = document.getElementById(barId);
        const value = document.getElementById(valueId);
        
        if (bar) {
            bar.style.width = `${Math.max(0, percentage)}%`;
        }
        if (value) {
            value.textContent = `${Math.floor(percentage)}%`;
        }
    }
    
    // الحصول على معامل التكبير الحالي
    getScaleFactor() {
        return this.scaleFactor;
    }
    
    // فحص حالة الشاشة الكاملة
    isInFullscreen() {
        return this.isFullscreen;
    }
}
