# 🛠️ دليل التطوير - Development Guide

هذا الدليل مخصص للمطورين الذين يريدون تطوير أو تخصيص اللعبة.

## 🏗️ هيكل الكود

### 📁 تنظيم الملفات:
- **js/config.js**: جميع إعدادات اللعبة والثوابت
- **js/audio.js**: نظام إدارة الصوت
- **js/particles.js**: نظام الجسيمات والطقس
- **js/effects.js**: المؤثرات البصرية والخلفية
- **js/player.js**: فئة اللاعب وتحكمه
- **js/enemies.js**: فئة الأعداء وإدارتهم
- **js/collectibles.js**: العناصر القابلة للجمع
- **js/obstacles.js**: العوائق والحواجز
- **js/ui.js**: واجهة المستخدم والقوائم
- **js/game.js**: منطق اللعبة الرئيسي
- **js/main.js**: نقطة البداية والتهيئة

## ⚙️ التخصيص والتعديل

### 🎮 تعديل إعدادات اللعبة:
```javascript
// في ملف js/config.js
CONFIG.PLAYER.SPEED = 7; // تغيير سرعة اللاعب
CONFIG.GAME.INITIAL_SPEED = 3; // تغيير السرعة الابتدائية
CONFIG.ENEMIES.MIN_SPAWN_INTERVAL = 30; // تغيير معدل ظهور الأعداء
```

### 🎨 إضافة مؤثرات جديدة:
```javascript
// في ملف js/particles.js
particleSystem.createCustomEffect(x, y, {
    COUNT: 10,
    LIFE: 30,
    SPEED: 5,
    COLORS: ['#ff0000', '#00ff00', '#0000ff']
});
```

### 🚗 إضافة نوع سيارة جديد:
```javascript
// في ملف js/enemies.js - في دالة setupByType
case 'newCarType':
    this.width = 45;
    this.height = 70;
    this.color = '#purple';
    this.health = 2;
    break;
```

### 🏆 إضافة إنجازات جديدة:
```javascript
// في ملف js/ui.js - في دالة updateAchievements
if (stats.specialCondition) {
    this.achievements.push('🌟 إنجاز جديد!');
}
```

## 🔧 أدوات التطوير

### 🐛 أدوات التصحيح:
```javascript
// في وحدة تحكم المتصفح
gameDebug.getGameState();     // حالة اللعبة الحالية
gameDebug.getScore();         // النقاط الحالية
gameDebug.toggleGodMode();    // تفعيل وضع الحماية
gameDebug.addScore(1000);     // إضافة نقاط
```

### 📊 مراقبة الأداء:
```javascript
// إضافة مراقب FPS
let fps = 0;
let lastTime = performance.now();

function updateFPS() {
    const now = performance.now();
    fps = 1000 / (now - lastTime);
    lastTime = now;
    console.log(`FPS: ${fps.toFixed(1)}`);
}
```

## 🎯 إضافة ميزات جديدة

### 1. إضافة نوع عائق جديد:
```javascript
// في js/obstacles.js
class LaserBarrier extends Obstacle {
    constructor(x, y) {
        super(x, y, 'laser');
        this.animationPhase = 0;
    }
    
    draw(ctx) {
        // رسم شعاع الليزر
        ctx.strokeStyle = '#ff0000';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(this.x, this.y);
        ctx.lineTo(this.x + this.width, this.y);
        ctx.stroke();
    }
}
```

### 2. إضافة نوع مكافأة جديد:
```javascript
// في js/collectibles.js
case 'shield':
    this.collectShield(player, effects, particles);
    break;

collectShield(player, effects, particles) {
    player.shielded = true;
    player.shieldTimer = 300; // 5 ثواني
    effects.addFlash('#00ffff', 0.3, 10);
}
```

### 3. إضافة تأثير طقس جديد:
```javascript
// في js/particles.js - في WeatherSystem
startSnow() {
    this.weatherType = 'snow';
    this.snowFlakes = [];
}

updateSnow() {
    // إضافة رقاقات ثلج جديدة
    for (let i = 0; i < 20; i++) {
        if (Math.random() < 0.1) {
            this.snowFlakes.push({
                x: Math.random() * CONFIG.CANVAS.WIDTH,
                y: -10,
                speed: 2 + Math.random() * 2,
                size: 2 + Math.random() * 3
            });
        }
    }
}
```

## 🎨 تخصيص المظهر

### 🌈 تغيير الألوان:
```css
/* في style.css */
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
    --accent-color: #your-color;
}
```

### 🖼️ إضافة خلفيات مخصصة:
```javascript
// في js/effects.js - في BackgroundManager
drawCustomBackground(ctx) {
    // رسم خلفية مخصصة
    const gradient = ctx.createLinearGradient(0, 0, 0, CONFIG.CANVAS.HEIGHT);
    gradient.addColorStop(0, '#your-top-color');
    gradient.addColorStop(1, '#your-bottom-color');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, CONFIG.CANVAS.WIDTH, CONFIG.CANVAS.HEIGHT);
}
```

## 📱 تحسين الأداء

### ⚡ نصائح للأداء:
1. **تقليل عدد الجسيمات** على الأجهزة الضعيفة
2. **استخدام Object Pooling** للكائنات المتكررة
3. **تحسين دورة الرسم** بتجنب العمليات المكلفة
4. **ضغط الصور والأصوات** لتقليل وقت التحميل

### 🔄 Object Pooling مثال:
```javascript
class ObjectPool {
    constructor(createFn, resetFn, initialSize = 10) {
        this.createFn = createFn;
        this.resetFn = resetFn;
        this.pool = [];
        
        for (let i = 0; i < initialSize; i++) {
            this.pool.push(this.createFn());
        }
    }
    
    get() {
        if (this.pool.length > 0) {
            return this.pool.pop();
        }
        return this.createFn();
    }
    
    release(obj) {
        this.resetFn(obj);
        this.pool.push(obj);
    }
}
```

## 🧪 الاختبار

### 🔍 اختبار الوظائف:
```javascript
// اختبار نظام التصادم
function testCollisionSystem() {
    const player = { x: 100, y: 100, width: 40, height: 60 };
    const enemy = { x: 110, y: 110, width: 40, height: 60 };
    
    const collision = isColliding(player, enemy);
    console.assert(collision === true, 'Collision detection failed');
}
```

### 📊 اختبار الأداء:
```javascript
// قياس وقت تنفيذ دالة
function measurePerformance(fn, name) {
    const start = performance.now();
    fn();
    const end = performance.now();
    console.log(`${name}: ${end - start}ms`);
}
```

## 🚀 النشر والتوزيع

### 📦 تحضير للنشر:
1. **ضغط ملفات JavaScript** باستخدام أدوات مثل UglifyJS
2. **تحسين ملفات CSS** بإزالة التعليقات والمسافات
3. **ضغط الصور** باستخدام أدوات مثل TinyPNG
4. **اختبار على متصفحات مختلفة**

### 🌐 رفع على الاستضافة:
```bash
# رفع الملفات عبر FTP أو SFTP
scp -r * user@server:/path/to/web/directory/

# أو استخدام Git للنشر
git add .
git commit -m "Deploy game"
git push origin main
```

## 📚 مصادر إضافية

- [MDN Canvas API](https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API)
- [Web Audio API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Audio_API)
- [Game Development Patterns](https://gameprogrammingpatterns.com/)
- [HTML5 Game Development](https://html5gamedevs.com/)

---

**نصيحة**: ابدأ بتعديلات صغيرة واختبر كل تغيير قبل الانتقال للتالي!
