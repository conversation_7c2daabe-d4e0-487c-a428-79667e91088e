* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.game-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
}

.header {
    text-align: center;
    margin-bottom: 20px;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.score-board {
    display: flex;
    justify-content: space-around;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 10px;
}

.score-board > div {
    text-align: center;
    font-weight: bold;
}

.game-area {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#gameCanvas {
    border: 3px solid #fff;
    border-radius: 10px;
    background: #333;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
}

.start-screen, .game-over-screen, .pause-screen {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.start-screen h2, .game-over-screen h2, .pause-screen h2 {
    font-size: 2em;
    margin-bottom: 15px;
}

.start-screen p, .game-over-screen p {
    margin-bottom: 10px;
    font-size: 1.1em;
}

.game-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.2em;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    font-weight: bold;
}

.game-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.controls {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.controls h3 {
    margin-bottom: 10px;
    font-size: 1.3em;
}

.control-keys {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 10px;
}

.key-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.key {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 5px;
    font-weight: bold;
    min-width: 30px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 600px) {
    .game-container {
        margin: 10px;
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .score-board {
        flex-direction: column;
        gap: 5px;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 350px;
        height: auto;
    }
    
    .control-keys {
        justify-content: center;
    }
}

/* Game HUD Styles */
.game-hud {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-top: 15px;
}

.fuel-meter, .nitro-meter, .health-meter {
    flex: 1;
    text-align: center;
}

.meter-label {
    font-size: 0.9em;
    margin-bottom: 5px;
    font-weight: bold;
}

.meter-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 3px;
}

.meter-fill {
    height: 100%;
    background: #2ecc71;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.nitro-fill {
    background: linear-gradient(90deg, #3498db, #74b9ff);
}

.health-fill {
    background: linear-gradient(90deg, #2ecc71, #00b894);
}

.meter-value {
    font-size: 0.8em;
    font-weight: bold;
}

/* Mini Map */
.mini-map {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 8px;
    padding: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.mini-map-label {
    color: white;
    font-size: 10px;
    text-align: center;
    margin-top: 5px;
}

#miniMapCanvas {
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 4px;
}

/* Sound Controls */
.sound-controls {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    gap: 10px;
}

.sound-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.sound-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Difficulty Selector */
.difficulty-selector {
    margin: 20px 0;
}

.difficulty-selector h3 {
    margin-bottom: 10px;
    font-size: 1.1em;
}

.difficulty-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.difficulty-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.difficulty-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.difficulty-btn.active {
    background: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.05);
}

/* Final Stats */
.final-stats {
    margin: 15px 0;
    text-align: left;
}

.final-stats p {
    margin: 5px 0;
    padding: 5px 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    border-left: 3px solid #3498db;
}

/* Achievements */
.achievements {
    margin: 15px 0;
}

.achievement {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 20px;
    border: 1px solid #2ecc71;
    font-size: 0.9em;
    text-align: center;
}

/* Secondary Button */
.game-btn.secondary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    margin-left: 10px;
}

/* Level Up Effect */
.level-up-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: levelUpFade 3s ease-in-out;
}

.level-up-content {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    color: white;
    animation: levelUpBounce 0.6s ease-out;
}

@keyframes levelUpFade {
    0%, 100% { opacity: 0; }
    20%, 80% { opacity: 1; }
}

@keyframes levelUpBounce {
    0% { transform: scale(0.3); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Animation for score updates */
@keyframes scoreUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #ffff00; }
    100% { transform: scale(1); }
}

.score-update {
    animation: scoreUpdate 0.3s ease;
}
