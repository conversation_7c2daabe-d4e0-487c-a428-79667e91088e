* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.game-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
}

.header {
    text-align: center;
    margin-bottom: 20px;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.score-board {
    display: flex;
    justify-content: space-around;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 10px;
}

.score-board > div {
    text-align: center;
    font-weight: bold;
}

.game-area {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#gameCanvas {
    border: 3px solid #fff;
    border-radius: 10px;
    background: #333;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
}

.start-screen, .game-over-screen, .pause-screen {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.start-screen h2, .game-over-screen h2, .pause-screen h2 {
    font-size: 2em;
    margin-bottom: 15px;
}

.start-screen p, .game-over-screen p {
    margin-bottom: 10px;
    font-size: 1.1em;
}

.game-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.2em;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    font-weight: bold;
}

.game-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.controls {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.controls h3 {
    margin-bottom: 10px;
    font-size: 1.3em;
}

.control-keys {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 10px;
}

.key-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.key {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 5px;
    font-weight: bold;
    min-width: 30px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 600px) {
    .game-container {
        margin: 10px;
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .score-board {
        flex-direction: column;
        gap: 5px;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 350px;
        height: auto;
    }
    
    .control-keys {
        justify-content: center;
    }
}

/* Animation for score updates */
@keyframes scoreUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #ffff00; }
    100% { transform: scale(1); }
}

.score-update {
    animation: scoreUpdate 0.3s ease;
}
