// Game variables
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const startScreen = document.getElementById('startScreen');
const gameOverScreen = document.getElementById('gameOverScreen');
const pauseScreen = document.getElementById('pauseScreen');
const gameOverlay = document.getElementById('gameOverlay');
const startBtn = document.getElementById('startBtn');
const restartBtn = document.getElementById('restartBtn');
const scoreElement = document.getElementById('score');
const highScoreElement = document.getElementById('highScore');
const speedElement = document.getElementById('speed');
const finalScoreElement = document.getElementById('finalScore');

// Game state
let gameState = 'start'; // 'start', 'playing', 'paused', 'gameOver'
let score = 0;
let highScore = localStorage.getItem('carGameHighScore') || 0;
let gameSpeed = 2;
let frameCount = 0;

// Player car
const player = {
    x: canvas.width / 2 - 20,
    y: canvas.height - 80,
    width: 40,
    height: 60,
    speed: 5,
    color: '#ff4757'
};

// Enemy cars array
let enemyCars = [];
const enemyCarColors = ['#3742fa', '#2ed573', '#ffa502', '#ff6348', '#a4b0be'];

// Road lines
let roadLines = [];

// Input handling
const keys = {};

// Initialize high score display
highScoreElement.textContent = highScore;

// Event listeners
startBtn.addEventListener('click', startGame);
restartBtn.addEventListener('click', restartGame);

document.addEventListener('keydown', (e) => {
    keys[e.key.toLowerCase()] = true;
    
    if (e.key === ' ') {
        e.preventDefault();
        if (gameState === 'playing') {
            pauseGame();
        } else if (gameState === 'paused') {
            resumeGame();
        }
    }
});

document.addEventListener('keyup', (e) => {
    keys[e.key.toLowerCase()] = false;
});

// Game functions
function startGame() {
    gameState = 'playing';
    score = 0;
    gameSpeed = 2;
    frameCount = 0;
    enemyCars = [];
    roadLines = [];
    
    // Reset player position
    player.x = canvas.width / 2 - 20;
    player.y = canvas.height - 80;
    
    // Initialize road lines
    for (let i = 0; i < 10; i++) {
        roadLines.push({
            x: canvas.width / 2 - 2,
            y: i * 80,
            width: 4,
            height: 40
        });
    }
    
    gameOverlay.style.display = 'none';
    gameLoop();
}

function pauseGame() {
    gameState = 'paused';
    startScreen.style.display = 'none';
    gameOverScreen.style.display = 'none';
    pauseScreen.style.display = 'block';
    gameOverlay.style.display = 'flex';
}

function resumeGame() {
    gameState = 'playing';
    gameOverlay.style.display = 'none';
    gameLoop();
}

function restartGame() {
    startGame();
}

function gameOver() {
    gameState = 'gameOver';
    finalScoreElement.textContent = score;
    
    if (score > highScore) {
        highScore = score;
        localStorage.setItem('carGameHighScore', highScore);
        highScoreElement.textContent = highScore;
    }
    
    startScreen.style.display = 'none';
    pauseScreen.style.display = 'none';
    gameOverScreen.style.display = 'block';
    gameOverlay.style.display = 'flex';
}

function updatePlayer() {
    // Handle input
    if (keys['arrowleft'] || keys['a']) {
        player.x -= player.speed;
    }
    if (keys['arrowright'] || keys['d']) {
        player.x += player.speed;
    }
    if (keys['arrowup'] || keys['w']) {
        player.y -= player.speed;
    }
    if (keys['arrowdown'] || keys['s']) {
        player.y += player.speed;
    }
    
    // Keep player within canvas bounds
    player.x = Math.max(0, Math.min(canvas.width - player.width, player.x));
    player.y = Math.max(0, Math.min(canvas.height - player.height, player.y));
}

function updateEnemyCars() {
    // Move existing enemy cars
    for (let i = enemyCars.length - 1; i >= 0; i--) {
        enemyCars[i].y += gameSpeed;
        
        // Remove cars that are off screen
        if (enemyCars[i].y > canvas.height) {
            enemyCars.splice(i, 1);
            score += 10;
            updateScore();
        }
    }
    
    // Spawn new enemy cars
    if (frameCount % Math.max(60 - Math.floor(gameSpeed * 5), 20) === 0) {
        spawnEnemyCar();
    }
}

function spawnEnemyCar() {
    const lanes = [50, 150, 250, 350];
    const lane = lanes[Math.floor(Math.random() * lanes.length)];
    
    enemyCars.push({
        x: lane - 20,
        y: -60,
        width: 40,
        height: 60,
        color: enemyCarColors[Math.floor(Math.random() * enemyCarColors.length)]
    });
}

function updateRoadLines() {
    for (let line of roadLines) {
        line.y += gameSpeed;
        
        if (line.y > canvas.height) {
            line.y = -40;
        }
    }
}

function checkCollisions() {
    for (let enemy of enemyCars) {
        if (player.x < enemy.x + enemy.width &&
            player.x + player.width > enemy.x &&
            player.y < enemy.y + enemy.height &&
            player.y + player.height > enemy.y) {
            gameOver();
            return;
        }
    }
}

function updateScore() {
    scoreElement.textContent = score;
    scoreElement.classList.add('score-update');
    setTimeout(() => scoreElement.classList.remove('score-update'), 300);
}

function updateSpeed() {
    // Increase game speed gradually
    gameSpeed = 2 + (score / 500);
    speedElement.textContent = Math.floor(gameSpeed * 20);
}

function drawPlayer() {
    // Car body
    ctx.fillStyle = player.color;
    ctx.fillRect(player.x, player.y, player.width, player.height);
    
    // Car details
    ctx.fillStyle = '#2c2c2c';
    ctx.fillRect(player.x + 5, player.y + 10, player.width - 10, 15);
    ctx.fillRect(player.x + 5, player.y + 35, player.width - 10, 15);
    
    // Wheels
    ctx.fillStyle = '#000';
    ctx.fillRect(player.x - 3, player.y + 5, 6, 12);
    ctx.fillRect(player.x + player.width - 3, player.y + 5, 6, 12);
    ctx.fillRect(player.x - 3, player.y + 43, 6, 12);
    ctx.fillRect(player.x + player.width - 3, player.y + 43, 6, 12);
}

function drawEnemyCars() {
    for (let enemy of enemyCars) {
        // Car body
        ctx.fillStyle = enemy.color;
        ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
        
        // Car details
        ctx.fillStyle = '#2c2c2c';
        ctx.fillRect(enemy.x + 5, enemy.y + 10, enemy.width - 10, 15);
        ctx.fillRect(enemy.x + 5, enemy.y + 35, enemy.width - 10, 15);
        
        // Wheels
        ctx.fillStyle = '#000';
        ctx.fillRect(enemy.x - 3, enemy.y + 5, 6, 12);
        ctx.fillRect(enemy.x + enemy.width - 3, enemy.y + 5, 6, 12);
        ctx.fillRect(enemy.x - 3, enemy.y + 43, 6, 12);
        ctx.fillRect(enemy.x + enemy.width - 3, enemy.y + 43, 6, 12);
    }
}

function drawRoad() {
    // Road background
    ctx.fillStyle = '#404040';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Road edges
    ctx.fillStyle = '#fff';
    ctx.fillRect(20, 0, 4, canvas.height);
    ctx.fillRect(canvas.width - 24, 0, 4, canvas.height);
    
    // Center lines
    ctx.fillStyle = '#ffff00';
    for (let line of roadLines) {
        ctx.fillRect(line.x, line.y, line.width, line.height);
    }
}

function gameLoop() {
    if (gameState !== 'playing') return;
    
    frameCount++;
    
    // Update game objects
    updatePlayer();
    updateEnemyCars();
    updateRoadLines();
    updateSpeed();
    checkCollisions();
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw everything
    drawRoad();
    drawPlayer();
    drawEnemyCars();
    
    // Continue game loop
    requestAnimationFrame(gameLoop);
}
