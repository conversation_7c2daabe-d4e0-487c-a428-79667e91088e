# 🏎️ لعبة السيارات الاحترافية - Professional Car Racing Game

لعبة سيارات احترافية ومتطورة مطورة بـ HTML5 Canvas و JavaScript مع مؤثرات بصرية وصوتية متقدمة. يمكن رفعها على أي استضافة ويب بسهولة.

## 🎮 كيفية اللعب

### الهدف الأساسي:
- تجنب الاصطدام بالسيارات الأخرى والعوائق
- اجمع العملات والمكافآت
- احصل على أعلى نقاط ممكنة واوصل لأعلى مستوى

### التحكم:
- **الحركة**: استخدم الأسهم (↑ ↓ ← →) أو مفاتيح WASD
- **النيتروس**: اضغط Shift لتسريع إضافي
- **إيقاف/تشغيل**: اضغط مسافة
- **الشاشة الكاملة**: اضغط F11

### نظام النقاط:
- **10 نقاط** لكل سيارة تتجاوزها
- **5 نقاط** لكل عملة تجمعها
- **1 نقطة** لكل ثانية تبقى فيها على قيد الحياة
- **مكافآت إضافية** عند الوصول لمستويات جديدة

### الموارد:
- **الوقود**: ينفد تدريجياً، اجمع علب الوقود لإعادة التعبئة
- **النيتروس**: يتراكم عند جمع العملات، استخدمه للسرعة الإضافية
- **الصحة**: تقل عند الاصطدام، اجمع حقائب الإسعاف للعلاج

## 🚀 كيفية رفع اللعبة على الاستضافة

### الطريقة الأولى: رفع مباشر
1. ارفع جميع الملفات (index.html, style.css, game.js) إلى مجلد public_html أو www في استضافتك
2. تأكد من أن ملف index.html في المجلد الرئيسي
3. افتح الرابط في المتصفح: `http://yourdomain.com`

### الطريقة الثانية: استخدام مجلد فرعي
1. أنشئ مجلد جديد في استضافتك (مثل: `car-game`)
2. ارفع جميع الملفات داخل هذا المجلد
3. افتح الرابط: `http://yourdomain.com/car-game`

## 📁 هيكل الملفات

```
لعبة/
├── index.html              # الصفحة الرئيسية
├── style.css               # ملف التنسيق الرئيسي
├── js/                     # مجلد ملفات JavaScript
│   ├── config.js           # إعدادات اللعبة
│   ├── audio.js            # نظام الصوت
│   ├── particles.js        # نظام الجسيمات والطقس
│   ├── effects.js          # المؤثرات البصرية والخلفية
│   ├── player.js           # فئة اللاعب
│   ├── enemies.js          # فئة الأعداء
│   ├── collectibles.js     # العناصر القابلة للجمع
│   ├── obstacles.js        # العوائق
│   ├── ui.js               # واجهة المستخدم
│   ├── game.js             # منطق اللعبة الرئيسي
│   └── main.js             # نقطة البداية
├── sounds/                 # مجلد الأصوات (اختياري)
│   ├── engine.mp3          # صوت المحرك
│   ├── crash.mp3           # صوت التصادم
│   ├── coin.mp3            # صوت العملات
│   ├── nitro.mp3           # صوت النيتروس
│   ├── background.mp3      # الموسيقى الخلفية
│   └── README.md           # تعليمات الأصوات
└── README.md               # هذا الملف
```

## ✨ المميزات المتقدمة

### 🎨 المؤثرات البصرية:
- **نظام جسيمات متطور**: انفجارات، دخان العادم، شرر
- **تأثيرات الطقس**: مطر وضباب ديناميكي
- **إضاءة واقعية**: أضواء الشارع وأضواء السيارات
- **اهتزاز الشاشة**: عند التصادمات والانفجارات
- **تأثيرات الفلاش**: للتأكيد على الأحداث المهمة
- **خلفية متحركة**: أشجار ومباني وغيوم متحركة

### 🔊 النظام الصوتي:
- **أصوات اصطناعية**: مولدة بـ Web Audio API
- **صوت المحرك التفاعلي**: يتغير حسب السرعة
- **مؤثرات صوتية متنوعة**: تصادم، عملات، نيتروس
- **موسيقى خلفية**: (اختيارية)
- **تحكم في الصوت**: إمكانية كتم الأصوات والموسيقى

### 🎮 ميزات اللعب:
- **3 مستويات صعوبة**: سهل، عادي، صعب
- **أنواع سيارات متعددة**: عادية، رياضية، شاحنات، شرطة
- **عوائق متنوعة**: حفر، بقع زيت، حواجز، مخاريط
- **مكافآت قابلة للجمع**: عملات، وقود، نيتروس، صحة
- **نظام مستويات**: تقدم تدريجي مع مكافآت
- **نظام إنجازات**: تحديات وأهداف للوصول إليها

### 📱 التوافق والاستجابة:
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **تحكم باللمس**: للأجهزة المحمولة مع عصا تحكم افتراضية
- **خريطة مصغرة**: لمتابعة موقع الأعداء والمكافآت
- **واجهة مستخدم متطورة**: مؤشرات للوقود والصحة والنيتروس
- **حفظ أعلى نقاط**: يحفظ في المتصفح مع إحصائيات مفصلة

### 🛠️ التقنيات المستخدمة:
- **HTML5 Canvas**: للرسم والرسوم المتحركة
- **Web Audio API**: للأصوات الاصطناعية
- **Local Storage**: لحفظ البيانات
- **Responsive Design**: للتوافق مع جميع الأجهزة
- **Modern JavaScript**: ES6+ مع فئات وحدات منظمة

## 🔧 متطلبات التشغيل

- متصفح ويب حديث يدعم HTML5 Canvas
- لا يحتاج إلى قاعدة بيانات أو خادم خاص
- يعمل على أي استضافة ويب عادية

## 🎯 تطوير اللعبة

يمكنك تطوير اللعبة بإضافة:
- أصوات للعبة
- مستويات مختلفة
- أنواع مختلفة من السيارات
- عوائق إضافية
- نظام حياة للاعب

## 📱 التوافق

- ✅ Chrome
- ✅ Firefox  
- ✅ Safari
- ✅ Edge
- ✅ الأجهزة المحمولة

## 🐛 استكشاف الأخطاء

إذا لم تعمل اللعبة:
1. تأكد من رفع جميع الملفات الثلاثة
2. تأكد من أن أسماء الملفات صحيحة
3. تحقق من وجود أخطاء في وحدة تحكم المتصفح (F12)
4. تأكد من أن الاستضافة تدعم HTML5

---

**استمتع باللعب! 🎮**
