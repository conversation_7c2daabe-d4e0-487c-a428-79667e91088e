# 🏎️ لعبة السيارات الاحترافية - Professional Car Racing Game

لعبة سيارات احترافية ومتطورة مطورة بـ HTML5 Canvas و JavaScript مع مؤثرات بصرية وصوتية متقدمة. يمكن رفعها على أي استضافة ويب بسهولة.

## 🎮 كيفية اللعب

### الهدف الأساسي:
- تجنب الاصطدام بالسيارات الأخرى والعوائق
- اجمع العملات والمكافآت
- احصل على أعلى نقاط ممكنة واوصل لأعلى مستوى

### التحكم:

#### ⌨️ لوحة المفاتيح:
- **الحركة**: استخدم الأسهم (↑ ↓ ← →) أو مفاتيح WASD
- **النيتروس**: اضغط Shift لتسريع إضافي
- **إيقاف/تشغيل**: اضغط مسافة
- **الشاشة الكاملة**: اضغط F11

#### 🎮 الجويستيك (Gamepad):
- **الحركة**: العصا التناظرية اليسرى
- **النيتروس**: زر X (Square على PlayStation)
- **تأكيد**: زر A (X على PlayStation)
- **إيقاف/تشغيل**: زر Start (Options على PlayStation)
- **الشاشة الكاملة**: زر Y (Triangle على PlayStation)
- **إلغاء**: زر B (Circle على PlayStation)
- **خيارات**: زر Back (Share على PlayStation)

### نظام النقاط:
- **10 نقاط** لكل سيارة تتجاوزها
- **5 نقاط** لكل عملة تجمعها
- **1 نقطة** لكل ثانية تبقى فيها على قيد الحياة
- **مكافآت إضافية** عند الوصول لمستويات جديدة

### الموارد:
- **الوقود**: ينفد تدريجياً (محسن ليدوم أطول)، اجمع علب الوقود لإعادة التعبئة
- **النيتروس**: يتراكم عند جمع العملات، استخدمه للسرعة الإضافية
- **الصحة**: تقل عند الاصطدام، اجمع حقائب الإسعاف للعلاج

### القوى الخاصة الجديدة:
- **🛡️ الدرع**: حماية مؤقتة من التصادمات
- **🧲 المغناطيس**: يجذب العملات تلقائياً
- **⭐ النقاط المضاعفة**: مضاعفة النقاط لفترة محدودة
- **⏰ الحركة البطيئة**: إبطاء الوقت لتجنب العوائق

### نظام الكومبو:
- **سلسلة النقاط**: اجمع العملات بشكل متتالي لزيادة المضاعف
- **مضاعف النقاط**: يزداد مع كل عملة متتالية (حتى x2.0)
- **مؤقت الكومبو**: 3 ثواني للحفاظ على السلسلة

## 🚀 كيفية رفع اللعبة على الاستضافة

### الطريقة الأولى: رفع مباشر
1. ارفع جميع الملفات (index.html, style.css, game.js) إلى مجلد public_html أو www في استضافتك
2. تأكد من أن ملف index.html في المجلد الرئيسي
3. افتح الرابط في المتصفح: `http://yourdomain.com`

### الطريقة الثانية: استخدام مجلد فرعي
1. أنشئ مجلد جديد في استضافتك (مثل: `car-game`)
2. ارفع جميع الملفات داخل هذا المجلد
3. افتح الرابط: `http://yourdomain.com/car-game`

## 📁 هيكل الملفات

```
لعبة/
├── index.html              # الصفحة الرئيسية
├── style.css               # ملف التنسيق الرئيسي
├── js/                     # مجلد ملفات JavaScript
│   ├── config.js           # إعدادات اللعبة
│   ├── audio.js            # نظام الصوت
│   ├── particles.js        # نظام الجسيمات والطقس
│   ├── effects.js          # المؤثرات البصرية والخلفية
│   ├── player.js           # فئة اللاعب
│   ├── enemies.js          # فئة الأعداء
│   ├── collectibles.js     # العناصر القابلة للجمع
│   ├── obstacles.js        # العوائق
│   ├── ui.js               # واجهة المستخدم
│   ├── game.js             # منطق اللعبة الرئيسي
│   └── main.js             # نقطة البداية
├── sounds/                 # مجلد الأصوات (اختياري)
│   ├── engine.mp3          # صوت المحرك
│   ├── crash.mp3           # صوت التصادم
│   ├── coin.mp3            # صوت العملات
│   ├── nitro.mp3           # صوت النيتروس
│   ├── background.mp3      # الموسيقى الخلفية
│   └── README.md           # تعليمات الأصوات
└── README.md               # هذا الملف
```

## ✨ المميزات المتقدمة

### 🎨 المؤثرات البصرية:
- **نظام جسيمات متطور**: انفجارات، دخان العادم، شرر
- **تأثيرات الطقس**: مطر وضباب ديناميكي
- **إضاءة واقعية**: أضواء الشارع وأضواء السيارات
- **اهتزاز الشاشة**: عند التصادمات والانفجارات
- **تأثيرات الفلاش**: للتأكيد على الأحداث المهمة
- **خلفية متحركة**: أشجار ومباني وغيوم متحركة

### 🔊 النظام الصوتي:
- **أصوات اصطناعية**: مولدة بـ Web Audio API
- **صوت المحرك التفاعلي**: يتغير حسب السرعة
- **مؤثرات صوتية متنوعة**: تصادم، عملات، نيتروس
- **موسيقى خلفية**: (اختيارية)
- **تحكم في الصوت**: إمكانية كتم الأصوات والموسيقى

### 🎮 ميزات اللعب المتطورة:
- **3 مستويات صعوبة**: سهل، عادي، صعب مع تأثيرات مختلفة
- **🚗 أنواع سيارات ذكية**: عادية، رياضية، شاحنات، شرطة مع **حركة يمين ويسار ذكية**
- **🚧 عوائق متنوعة**: حفر، بقع زيت، حواجز، مخاريط مع تأثيرات واقعية
- **💎 مكافآت متطورة**: عملات، وقود، نيتروس، صحة + **4 قوى خاصة جديدة**
- **🆙 نظام مستويات**: تقدم تدريجي مع مكافآت ومؤثرات خاصة
- **🏆 نظام إنجازات**: تحديات وأهداف متنوعة للوصول إليها
- **🔥 نظام كومبو**: مضاعفة النقاط للعملات المتتالية (حتى x2.0)
- **🤖 ذكاء اصطناعي للسيارات**: تغيير المسارات وحركة واقعية
- **⛽ استهلاك وقود محسن**: يدوم لفترة أطول لتجربة لعب أفضل

### 🌟 القوى الخاصة الجديدة:
- **🛡️ الدرع (Shield)**: حماية مؤقتة من جميع التصادمات
- **🧲 المغناطيس (Magnet)**: يجذب العملات تلقائياً من مسافة بعيدة
- **⭐ النقاط المضاعفة (Double Score)**: مضاعفة جميع النقاط لفترة محدودة
- **⏰ الحركة البطيئة (Slow Motion)**: إبطاء الوقت لتجنب العوائق بسهولة

### 🎯 نظام الكومبو المتطور:
- **سلسلة النقاط**: اجمع العملات بشكل متتالي لزيادة المضاعف
- **مضاعف تدريجي**: يبدأ من x1.1 ويصل حتى x2.0
- **مؤقت الكومبو**: 3 ثواني للحفاظ على السلسلة
- **مؤشر بصري**: عرض الكومبو الحالي والمضاعف

### 🎮 دعم الجويستيك الكامل:
- **🎯 دعم جميع أنواع الجويستيك**: Xbox، PlayStation، وأي جويستيك متوافق
- **🔄 كشف تلقائي**: يتم كشف الجويستيك تلقائياً عند التوصيل
- **📳 اهتزاز تفاعلي**: اهتزاز عند التصادم والنيتروس (إذا كان مدعوماً)
- **🎛️ تحكم كامل**: جميع وظائف اللعبة متاحة عبر الجويستيك
- **⚙️ منطقة ميتة**: تجنب الحركة غير المرغوبة للعصا التناظرية

### 🖥️ وضع الشاشة الكاملة المتطور:
- **📏 تكبير ذكي**: يحافظ على نسبة العرض إلى الارتفاع
- **🎨 واجهة مخصصة**: واجهة مستخدم مصممة خصيصاً للشاشة الكاملة
- **🎮 مساعدة الجويستيك**: عرض تعليمات الجويستيك في الشاشة الكاملة
- **⌨️ اختصارات سريعة**: F11 أو ESC للدخول والخروج
- **📱 متجاوب**: يعمل على جميع أحجام الشاشات

### 📱 التوافق والاستجابة:
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **تحكم باللمس**: للأجهزة المحمولة مع عصا تحكم افتراضية
- **🎮 دعم الجويستيك**: Xbox، PlayStation، وجميع الأنواع المتوافقة
- **🖥️ شاشة كاملة**: تجربة غامرة مع واجهة مخصصة
- **خريطة مصغرة ملونة**: لمتابعة موقع الأعداء والمكافآت بألوان مختلفة
- **واجهة مستخدم متطورة**: مؤشرات للوقود والصحة والنيتروس + عرض القوى الخاصة
- **حفظ أعلى نقاط**: يحفظ في المتصفح مع إحصائيات مفصلة

### 🛠️ التقنيات المستخدمة:
- **HTML5 Canvas**: للرسم والرسوم المتحركة
- **Web Audio API**: للأصوات الاصطناعية
- **Local Storage**: لحفظ البيانات
- **Responsive Design**: للتوافق مع جميع الأجهزة
- **Modern JavaScript**: ES6+ مع فئات وحدات منظمة

## 🔧 متطلبات التشغيل

- متصفح ويب حديث يدعم HTML5 Canvas
- لا يحتاج إلى قاعدة بيانات أو خادم خاص
- يعمل على أي استضافة ويب عادية

## 🎯 تطوير اللعبة

يمكنك تطوير اللعبة بإضافة:
- أصوات للعبة
- مستويات مختلفة
- أنواع مختلفة من السيارات
- عوائق إضافية
- نظام حياة للاعب

## 📱 التوافق

- ✅ Chrome
- ✅ Firefox  
- ✅ Safari
- ✅ Edge
- ✅ الأجهزة المحمولة

## 🐛 استكشاف الأخطاء

إذا لم تعمل اللعبة:
1. تأكد من رفع جميع الملفات الثلاثة
2. تأكد من أن أسماء الملفات صحيحة
3. تحقق من وجود أخطاء في وحدة تحكم المتصفح (F12)
4. تأكد من أن الاستضافة تدعم HTML5

---

**استمتع باللعب! 🎮**
