# 🏎️ لعبة السيارات - Car Racing Game

لعبة سيارات بسيطة وممتعة مطورة بـ HTML5 Canvas و JavaScript. يمكن رفعها على أي استضافة ويب بسهولة.

## 🎮 كيفية اللعب

- **الهدف**: تجنب الاصطدام بالسيارات الأخرى واحصل على أعلى نقاط ممكنة
- **التحكم**: 
  - استخدم الأسهم (↑ ↓ ← →) أو مفاتيح WASD للتحرك
  - اضغط مسافة لإيقاف/تشغيل اللعبة
- **النقاط**: تحصل على 10 نقاط لكل سيارة تتجاوزها
- **السرعة**: تزداد سرعة اللعبة تدريجياً مع زيادة النقاط

## 🚀 كيفية رفع اللعبة على الاستضافة

### الطريقة الأولى: رفع مباشر
1. ارفع جميع الملفات (index.html, style.css, game.js) إلى مجلد public_html أو www في استضافتك
2. تأكد من أن ملف index.html في المجلد الرئيسي
3. افتح الرابط في المتصفح: `http://yourdomain.com`

### الطريقة الثانية: استخدام مجلد فرعي
1. أنشئ مجلد جديد في استضافتك (مثل: `car-game`)
2. ارفع جميع الملفات داخل هذا المجلد
3. افتح الرابط: `http://yourdomain.com/car-game`

## 📁 هيكل الملفات

```
لعبة/
├── index.html      # الصفحة الرئيسية
├── style.css       # ملف التنسيق
├── game.js         # منطق اللعبة
└── README.md       # هذا الملف
```

## ✨ المميزات

- **تصميم متجاوب**: يعمل على جميع الأجهزة (كمبيوتر، تابلت، موبايل)
- **حفظ أعلى نقاط**: يحفظ أعلى نقاط في المتصفح
- **تحكم سهل**: يدعم لوحة المفاتيح والأسهم
- **رسوم بسيطة**: مرسومة بـ Canvas بدون صور خارجية
- **بدون مكتبات خارجية**: يعمل بـ HTML/CSS/JS فقط

## 🔧 متطلبات التشغيل

- متصفح ويب حديث يدعم HTML5 Canvas
- لا يحتاج إلى قاعدة بيانات أو خادم خاص
- يعمل على أي استضافة ويب عادية

## 🎯 تطوير اللعبة

يمكنك تطوير اللعبة بإضافة:
- أصوات للعبة
- مستويات مختلفة
- أنواع مختلفة من السيارات
- عوائق إضافية
- نظام حياة للاعب

## 📱 التوافق

- ✅ Chrome
- ✅ Firefox  
- ✅ Safari
- ✅ Edge
- ✅ الأجهزة المحمولة

## 🐛 استكشاف الأخطاء

إذا لم تعمل اللعبة:
1. تأكد من رفع جميع الملفات الثلاثة
2. تأكد من أن أسماء الملفات صحيحة
3. تحقق من وجود أخطاء في وحدة تحكم المتصفح (F12)
4. تأكد من أن الاستضافة تدعم HTML5

---

**استمتع باللعب! 🎮**
