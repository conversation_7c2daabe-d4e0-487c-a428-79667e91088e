{"name": "professional-car-racing-game", "version": "2.0.0", "description": "لعبة سيارات احترافية مطورة بـ HTML5 Canvas و JavaScript مع مؤثرات بصرية وصوتية متقدمة", "main": "index.html", "scripts": {"start": "python -m http.server 8000", "dev": "python -m http.server 8000", "build": "echo 'No build process needed for vanilla JS game'", "test": "echo 'No tests specified'", "deploy": "echo 'Upload files to your web hosting service'"}, "keywords": ["game", "racing", "car", "html5", "canvas", "javascript", "arabic", "web-game", "browser-game", "particles", "effects", "professional"], "author": {"name": "Car Racing Game Developer", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/professional-car-racing-game.git"}, "bugs": {"url": "https://github.com/yourusername/professional-car-racing-game/issues"}, "homepage": "https://yourusername.github.io/professional-car-racing-game", "engines": {"node": ">=12.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie <= 11"], "devDependencies": {}, "dependencies": {}, "files": ["index.html", "style.css", "js/", "sounds/", "README.md", "DEVELOPMENT.md"], "directories": {"lib": "js", "doc": "."}, "config": {"port": 8000}}