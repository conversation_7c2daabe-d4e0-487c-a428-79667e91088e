// Game Configuration
const CONFIG = {
    // Canvas settings
    CANVAS: {
        WIDTH: 400,
        HEIGHT: 600,
        LANES: [50, 150, 250, 350],
        LANE_WIDTH: 100
    },
    
    // Player settings
    PLAYER: {
        WIDTH: 40,
        HEIGHT: 60,
        SPEED: 5,
        NITRO_SPEED: 8,
        START_X: 180,
        START_Y: 520,
        MAX_HEALTH: 100,
        DAMAGE_PER_HIT: 25
    },
    
    // Enemy settings
    ENEMIES: {
        WIDTH: 40,
        HEIGHT: 60,
        MIN_SPAWN_INTERVAL: 20,
        MAX_SPAWN_INTERVAL: 80,
        COLORS: ['#3742fa', '#2ed573', '#ffa502', '#ff6348', '#a4b0be', '#8e44ad', '#e74c3c'],
        MOVEMENT_SPEED: 1.5, // سرعة حركة السيارات يمين ويسار
        MOVEMENT_CHANCE: 0.02, // احتمالية تغيير الاتجاه
        LANE_CHANGE_SPEED: 2 // سرعة تغيير المسار
    },
    
    // Game mechanics
    GAME: {
        INITIAL_SPEED: 2,
        SPEED_INCREMENT: 0.002,
        MAX_SPEED: 8,
        SCORE_PER_ENEMY: 10,
        SCORE_PER_COIN: 5,
        FUEL_CONSUMPTION: 0.02, // تقليل استهلاك الوقود
        FUEL_REFILL: 40, // زيادة كمية التعبئة
        NITRO_CONSUMPTION: 1.5, // تقليل استهلاك النيتروس
        NITRO_GAIN: 2, // زيادة كسب النيتروس
        LEVEL_SCORE_THRESHOLD: 500,
        COMBO_MULTIPLIER: 1.5, // مضاعف النقاط للكومبو
        MAX_COMBO: 10, // أقصى كومبو
        POWER_UP_DURATION: 300 // مدة القوى الخاصة
    },
    
    // Collectibles
    COLLECTIBLES: {
        COIN: {
            WIDTH: 20,
            HEIGHT: 20,
            COLOR: '#f1c40f',
            SPAWN_CHANCE: 0.3
        },
        FUEL: {
            WIDTH: 25,
            HEIGHT: 35,
            COLOR: '#e74c3c',
            SPAWN_CHANCE: 0.1
        },
        NITRO: {
            WIDTH: 25,
            HEIGHT: 35,
            COLOR: '#3498db',
            SPAWN_CHANCE: 0.08
        },
        HEALTH: {
            WIDTH: 25,
            HEIGHT: 25,
            COLOR: '#2ecc71',
            SPAWN_CHANCE: 0.05
        }
    },
    
    // Obstacles
    OBSTACLES: {
        POTHOLE: {
            WIDTH: 30,
            HEIGHT: 20,
            COLOR: '#2c3e50',
            DAMAGE: 15,
            SPAWN_CHANCE: 0.05
        },
        OIL_SPILL: {
            WIDTH: 40,
            HEIGHT: 30,
            COLOR: '#34495e',
            EFFECT_DURATION: 120,
            SPAWN_CHANCE: 0.03
        }
    },
    
    // Particles
    PARTICLES: {
        EXPLOSION: {
            COUNT: 15,
            LIFE: 30,
            SPEED: 3,
            COLORS: ['#ff6b6b', '#ffa500', '#ffff00', '#ff4757']
        },
        EXHAUST: {
            COUNT: 3,
            LIFE: 20,
            SPEED: 1,
            COLORS: ['#95a5a6', '#bdc3c7', '#ecf0f1']
        },
        SPARKS: {
            COUNT: 8,
            LIFE: 15,
            SPEED: 4,
            COLORS: ['#f39c12', '#e67e22', '#d35400']
        },
        NITRO: {
            COUNT: 5,
            LIFE: 25,
            SPEED: 2,
            COLORS: ['#3498db', '#2980b9', '#74b9ff']
        }
    },
    
    // Difficulty levels
    DIFFICULTY: {
        EASY: {
            ENEMY_SPEED_MULTIPLIER: 0.7,
            SPAWN_RATE_MULTIPLIER: 0.8,
            DAMAGE_MULTIPLIER: 0.5,
            FUEL_CONSUMPTION_MULTIPLIER: 0.7
        },
        NORMAL: {
            ENEMY_SPEED_MULTIPLIER: 1.0,
            SPAWN_RATE_MULTIPLIER: 1.0,
            DAMAGE_MULTIPLIER: 1.0,
            FUEL_CONSUMPTION_MULTIPLIER: 1.0
        },
        HARD: {
            ENEMY_SPEED_MULTIPLIER: 1.3,
            SPAWN_RATE_MULTIPLIER: 1.2,
            DAMAGE_MULTIPLIER: 1.5,
            FUEL_CONSUMPTION_MULTIPLIER: 1.3
        }
    },
    
    // Audio settings
    AUDIO: {
        MASTER_VOLUME: 0.7,
        SFX_VOLUME: 0.8,
        MUSIC_VOLUME: 0.5
    },
    
    // Weather effects
    WEATHER: {
        RAIN: {
            DROP_COUNT: 50,
            DROP_SPEED: 8,
            VISIBILITY_REDUCTION: 0.8
        },
        FOG: {
            OPACITY: 0.3,
            VISIBILITY_REDUCTION: 0.6
        },
        SNOW: {
            FLAKE_COUNT: 30,
            FLAKE_SPEED: 3,
            VISIBILITY_REDUCTION: 0.7
        }
    },

    // Power-ups
    POWER_UPS: {
        SHIELD: {
            WIDTH: 25,
            HEIGHT: 25,
            COLOR: '#00ffff',
            DURATION: 300,
            SPAWN_CHANCE: 0.03
        },
        MAGNET: {
            WIDTH: 25,
            HEIGHT: 25,
            COLOR: '#ff00ff',
            DURATION: 200,
            RANGE: 100,
            SPAWN_CHANCE: 0.02
        },
        DOUBLE_SCORE: {
            WIDTH: 25,
            HEIGHT: 25,
            COLOR: '#ffff00',
            DURATION: 250,
            SPAWN_CHANCE: 0.02
        },
        SLOW_MOTION: {
            WIDTH: 25,
            HEIGHT: 25,
            COLOR: '#00ff00',
            DURATION: 180,
            SPAWN_CHANCE: 0.015
        }
    },

    // Special effects
    SPECIAL_EFFECTS: {
        SCREEN_DISTORTION: true,
        MOTION_BLUR: true,
        PARTICLE_TRAILS: true,
        DYNAMIC_LIGHTING: true,
        CAMERA_SHAKE_INTENSITY: 1.0
    }
};

// Game states
const GAME_STATES = {
    START: 'start',
    PLAYING: 'playing',
    PAUSED: 'paused',
    GAME_OVER: 'gameOver',
    LEVEL_UP: 'levelUp'
};

// Input keys
const KEYS = {
    UP: ['arrowup', 'w'],
    DOWN: ['arrowdown', 's'],
    LEFT: ['arrowleft', 'a'],
    RIGHT: ['arrowright', 'd'],
    NITRO: ['shift'],
    PAUSE: [' ']
};
