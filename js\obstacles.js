// Obstacle Classes
class Obstacle {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.animationTimer = 0;
        this.setupByType();
    }
    
    setupByType() {
        const config = CONFIG.OBSTACLES[this.type.toUpperCase()];
        this.width = config.WIDTH;
        this.height = config.HEIGHT;
        this.color = config.COLOR;
        this.damage = config.DAMAGE || 0;
        this.effectDuration = config.EFFECT_DURATION || 0;
    }
    
    update(gameSpeed) {
        this.y += gameSpeed;
        this.animationTimer++;
        return this.y < CONFIG.CANVAS.HEIGHT + 50;
    }
    
    handleCollision(player, effects, particles) {
        switch (this.type) {
            case 'pothole':
                return this.handlePotholeCollision(player, effects, particles);
            case 'oil_spill':
                return this.handleOilSpillCollision(player, effects, particles);
            default:
                return false;
        }
    }
    
    handlePotholeCollision(player, effects, particles) {
        // Damage player
        const gameOver = player.takeDamage(this.damage, effects, particles);
        
        // Create dust particles
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 10,
                LIFE: 20,
                SPEED: 3,
                COLORS: ['#8b4513', '#a0522d', '#cd853f']
            }
        );
        
        // Screen shake
        effects.addScreenShake(6, 12);
        
        return gameOver;
    }
    
    handleOilSpillCollision(player, effects, particles) {
        // Make player skid
        player.skidding = true;
        player.skiddingTimer = this.effectDuration;
        
        // Create oil splash particles
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 8,
                LIFE: 30,
                SPEED: 2,
                COLORS: ['#2c3e50', '#34495e', '#1a1a1a']
            }
        );
        
        // Audio effect
        if (window.audioManager) {
            window.audioManager.playSound('brake');
        }
        
        return false;
    }
    
    draw(ctx) {
        ctx.save();
        
        switch (this.type) {
            case 'pothole':
                this.drawPothole(ctx);
                break;
            case 'oil_spill':
                this.drawOilSpill(ctx);
                break;
        }
        
        ctx.restore();
    }
    
    drawPothole(ctx) {
        // Pothole shadow/depth
        ctx.fillStyle = '#1a1a1a';
        ctx.beginPath();
        ctx.ellipse(
            this.x + this.width / 2,
            this.y + this.height / 2,
            this.width / 2,
            this.height / 2,
            0, 0, Math.PI * 2
        );
        ctx.fill();
        
        // Pothole edge
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.ellipse(
            this.x + this.width / 2,
            this.y + this.height / 2,
            this.width / 2 - 2,
            this.height / 2 - 2,
            0, 0, Math.PI * 2
        );
        ctx.fill();
        
        // Cracks around pothole
        ctx.strokeStyle = '#1a1a1a';
        ctx.lineWidth = 1;
        for (let i = 0; i < 5; i++) {
            const angle = (Math.PI * 2 * i) / 5;
            const startX = this.x + this.width / 2 + Math.cos(angle) * (this.width / 2 + 5);
            const startY = this.y + this.height / 2 + Math.sin(angle) * (this.height / 2 + 5);
            const endX = startX + Math.cos(angle) * 8;
            const endY = startY + Math.sin(angle) * 8;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
        }
    }
    
    drawOilSpill(ctx) {
        // Oil spill main body
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.ellipse(
            this.x + this.width / 2,
            this.y + this.height / 2,
            this.width / 2,
            this.height / 2,
            0, 0, Math.PI * 2
        );
        ctx.fill();
        
        // Oil spill shine effect
        const shineIntensity = 0.3 + Math.sin(this.animationTimer * 0.1) * 0.2;
        ctx.fillStyle = `rgba(100, 100, 100, ${shineIntensity})`;
        ctx.beginPath();
        ctx.ellipse(
            this.x + this.width / 2 - 3,
            this.y + this.height / 2 - 3,
            this.width / 3,
            this.height / 3,
            0, 0, Math.PI * 2
        );
        ctx.fill();
        
        // Oil droplets around main spill
        for (let i = 0; i < 3; i++) {
            const angle = (Math.PI * 2 * i) / 3 + this.animationTimer * 0.02;
            const dropX = this.x + this.width / 2 + Math.cos(angle) * (this.width / 2 + 8);
            const dropY = this.y + this.height / 2 + Math.sin(angle) * (this.height / 2 + 8);
            
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.arc(dropX, dropY, 2, 0, Math.PI * 2);
            ctx.fill();
        }
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// Road Barrier Class
class RoadBarrier extends Obstacle {
    constructor(x, y) {
        super(x, y, 'barrier');
        this.width = 60;
        this.height = 20;
        this.color = '#e74c3c';
        this.damage = 30;
        this.reflectiveStripes = [];
        this.initializeStripes();
    }
    
    initializeStripes() {
        for (let i = 0; i < 3; i++) {
            this.reflectiveStripes.push({
                x: i * 20,
                opacity: 0.8 + Math.random() * 0.2
            });
        }
    }
    
    handleCollision(player, effects, particles) {
        // Heavy damage and effects
        const gameOver = player.takeDamage(this.damage, effects, particles);
        
        // Create metal sparks
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 15,
                LIFE: 25,
                SPEED: 5,
                COLORS: ['#f39c12', '#e67e22', '#d35400', '#ffffff']
            }
        );
        
        // Strong screen shake
        effects.addScreenShake(12, 20);
        effects.addFlash('#ff0000', 0.6, 15);
        
        return gameOver;
    }
    
    draw(ctx) {
        ctx.save();
        
        // Barrier body
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // Reflective stripes
        this.reflectiveStripes.forEach(stripe => {
            ctx.fillStyle = `rgba(255, 255, 255, ${stripe.opacity})`;
            ctx.fillRect(this.x + stripe.x, this.y + 2, 15, this.height - 4);
        });
        
        // Warning text
        ctx.fillStyle = '#ffffff';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('DANGER', this.x + this.width / 2, this.y + this.height / 2 + 3);
        
        ctx.restore();
    }
}

// Construction Cone Class
class ConstructionCone extends Obstacle {
    constructor(x, y) {
        super(x, y, 'cone');
        this.width = 20;
        this.height = 30;
        this.color = '#ff6b35';
        this.damage = 10;
        this.wobble = 0;
    }
    
    update(gameSpeed) {
        const result = super.update(gameSpeed);
        this.wobble = Math.sin(this.animationTimer * 0.1) * 2;
        return result;
    }
    
    handleCollision(player, effects, particles) {
        // Light damage
        const gameOver = player.takeDamage(this.damage, effects, particles);
        
        // Create orange particles
        particles.createSparks(
            this.x + this.width / 2,
            this.y + this.height / 2,
            {
                COUNT: 6,
                LIFE: 15,
                SPEED: 2,
                COLORS: ['#ff6b35', '#ff8c42', '#ffa726']
            }
        );
        
        effects.addScreenShake(3, 8);
        
        return gameOver;
    }
    
    draw(ctx) {
        ctx.save();
        
        // Apply wobble
        ctx.translate(this.x + this.width / 2, this.y + this.height);
        ctx.rotate(this.wobble * Math.PI / 180);
        ctx.translate(-this.width / 2, -this.height);
        
        // Cone body
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.moveTo(this.width / 2, 0);
        ctx.lineTo(0, this.height);
        ctx.lineTo(this.width, this.height);
        ctx.closePath();
        ctx.fill();
        
        // Reflective stripes
        ctx.fillStyle = '#ffffff';
        for (let i = 0; i < 2; i++) {
            const y = 8 + i * 10;
            const width = (this.height - y) / this.height * this.width;
            const x = (this.width - width) / 2;
            ctx.fillRect(x, y, width, 3);
        }
        
        ctx.restore();
    }
}

// Obstacles Manager
class ObstaclesManager {
    constructor() {
        this.obstacles = [];
        this.spawnTimer = 0;
        this.spawnInterval = 200;
    }
    
    update(gameSpeed) {
        // Update existing obstacles
        this.obstacles = this.obstacles.filter(obstacle => obstacle.update(gameSpeed));
        
        // Spawn new obstacles
        this.spawnTimer++;
        if (this.spawnTimer >= this.spawnInterval) {
            this.spawnObstacle();
            this.spawnTimer = 0;
            this.spawnInterval = 150 + Math.random() * 200;
        }
    }
    
    spawnObstacle() {
        const lanes = CONFIG.CANVAS.LANES;
        const lane = lanes[Math.floor(Math.random() * lanes.length)];
        
        // Determine obstacle type based on probability
        const rand = Math.random();
        let obstacle;
        
        if (rand < CONFIG.OBSTACLES.POTHOLE.SPAWN_CHANCE) {
            obstacle = new Obstacle(
                lane - CONFIG.OBSTACLES.POTHOLE.WIDTH / 2,
                -CONFIG.OBSTACLES.POTHOLE.HEIGHT,
                'pothole'
            );
        } else if (rand < CONFIG.OBSTACLES.POTHOLE.SPAWN_CHANCE + CONFIG.OBSTACLES.OIL_SPILL.SPAWN_CHANCE) {
            obstacle = new Obstacle(
                lane - CONFIG.OBSTACLES.OIL_SPILL.WIDTH / 2,
                -CONFIG.OBSTACLES.OIL_SPILL.HEIGHT,
                'oil_spill'
            );
        } else if (rand < 0.12) {
            obstacle = new RoadBarrier(
                lane - 30,
                -20
            );
        } else if (rand < 0.15) {
            obstacle = new ConstructionCone(
                lane - 10,
                -30
            );
        }
        
        if (obstacle) {
            this.obstacles.push(obstacle);
        }
    }
    
    checkCollisions(player, effects, particles) {
        for (let i = this.obstacles.length - 1; i >= 0; i--) {
            const obstacle = this.obstacles[i];
            
            if (this.isColliding(player.getBounds(), obstacle.getBounds())) {
                const gameOver = obstacle.handleCollision(player, effects, particles);
                
                // Remove obstacle after collision (except oil spills)
                if (obstacle.type !== 'oil_spill') {
                    this.obstacles.splice(i, 1);
                }
                
                if (gameOver) return true;
            }
        }
        
        return false;
    }
    
    isColliding(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }
    
    draw(ctx) {
        this.obstacles.forEach(obstacle => obstacle.draw(ctx));
    }
    
    clear() {
        this.obstacles = [];
    }
}
