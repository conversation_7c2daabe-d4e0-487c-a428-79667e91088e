// Player Car Class
class Player {
    constructor() {
        this.reset();
        this.setupProperties();
    }
    
    reset() {
        this.x = CONFIG.PLAYER.START_X;
        this.y = CONFIG.PLAYER.START_Y;
        this.width = CONFIG.PLAYER.WIDTH;
        this.height = CONFIG.PLAYER.HEIGHT;
        this.speed = CONFIG.PLAYER.SPEED;
        this.health = CONFIG.PLAYER.MAX_HEALTH;
        this.fuel = 100;
        this.nitro = 0;
        this.isUsingNitro = false;
        this.invulnerable = false;
        this.invulnerabilityTimer = 0;
        this.skidding = false;
        this.rotation = 0;
        this.targetRotation = 0;
    }
    
    setupProperties() {
        this.color = '#ff4757';
        this.exhaustTimer = 0;
        this.nitroTimer = 0;
        this.damageFlash = 0;
        this.lastDirection = 'none';
    }
    
    update(keys, gameSpeed, effects, particles) {
        this.handleInput(keys, effects, particles);
        this.updatePhysics();
        this.updateEffects(gameSpeed, particles);
        this.updateTimers();
        this.constrainToCanvas();
    }
    
    handleInput(keys, effects, particles) {
        const timeMultiplier = effects.getTimeMultiplier();
        let moving = false;
        
        // Movement
        if (this.isKeyPressed(keys, KEYS.LEFT)) {
            this.x -= this.speed * timeMultiplier;
            this.targetRotation = -5;
            this.lastDirection = 'left';
            moving = true;
        }
        if (this.isKeyPressed(keys, KEYS.RIGHT)) {
            this.x += this.speed * timeMultiplier;
            this.targetRotation = 5;
            this.lastDirection = 'right';
            moving = true;
        }
        if (this.isKeyPressed(keys, KEYS.UP)) {
            this.y -= this.speed * timeMultiplier;
            moving = true;
        }
        if (this.isKeyPressed(keys, KEYS.DOWN)) {
            this.y += this.speed * timeMultiplier;
            moving = true;
        }
        
        // Nitro
        if (this.isKeyPressed(keys, KEYS.NITRO) && this.nitro > 0 && !this.isUsingNitro) {
            this.activateNitro(effects, particles);
        }
        
        if (!moving) {
            this.targetRotation = 0;
            this.lastDirection = 'none';
        }
    }
    
    isKeyPressed(keys, keyArray) {
        return keyArray.some(key => keys[key]);
    }
    
    updatePhysics() {
        // Smooth rotation
        this.rotation += (this.targetRotation - this.rotation) * 0.1;
        
        // Nitro effects
        if (this.isUsingNitro) {
            this.speed = CONFIG.PLAYER.NITRO_SPEED;
            this.nitro -= CONFIG.GAME.NITRO_CONSUMPTION;
            this.nitroTimer++;
            
            if (this.nitro <= 0 || this.nitroTimer > 60) {
                this.deactivateNitro();
            }
        } else {
            this.speed = CONFIG.PLAYER.SPEED;
        }
        
        // Fuel consumption
        this.fuel -= CONFIG.GAME.FUEL_CONSUMPTION;
        if (this.fuel < 0) this.fuel = 0;
    }
    
    updateEffects(gameSpeed, particles) {
        // Exhaust particles
        this.exhaustTimer++;
        if (this.exhaustTimer > 5) {
            particles.createExhaust(
                this.x + this.width / 2 + (Math.random() - 0.5) * 10,
                this.y + this.height
            );
            this.exhaustTimer = 0;
        }
        
        // Nitro particles
        if (this.isUsingNitro && this.nitroTimer % 3 === 0) {
            particles.createNitroFlame(
                this.x + this.width / 2,
                this.y + this.height
            );
        }
    }
    
    updateTimers() {
        if (this.invulnerable) {
            this.invulnerabilityTimer--;
            if (this.invulnerabilityTimer <= 0) {
                this.invulnerable = false;
            }
        }
        
        if (this.damageFlash > 0) {
            this.damageFlash--;
        }
    }
    
    constrainToCanvas() {
        this.x = Math.max(25, Math.min(CONFIG.CANVAS.WIDTH - this.width - 25, this.x));
        this.y = Math.max(0, Math.min(CONFIG.CANVAS.HEIGHT - this.height, this.y));
    }
    
    activateNitro(effects, particles) {
        this.isUsingNitro = true;
        this.nitroTimer = 0;
        
        // Visual effects
        effects.addFlash('#3498db', 0.3, 15);
        particles.createSparks(this.x + this.width / 2, this.y + this.height);
        
        // Audio
        if (window.audioManager) {
            window.audioManager.playSound('nitro');
        }
    }
    
    deactivateNitro() {
        this.isUsingNitro = false;
        this.nitroTimer = 0;
    }
    
    takeDamage(amount, effects, particles) {
        if (this.invulnerable) return false;
        
        this.health -= amount;
        this.damageFlash = 20;
        this.invulnerable = true;
        this.invulnerabilityTimer = 60;
        
        // Visual effects
        effects.addScreenShake(8, 15);
        effects.addFlash('#ff0000', 0.5, 10);
        particles.createExplosion(this.x + this.width / 2, this.y + this.height / 2);
        
        // Audio
        if (window.audioManager) {
            window.audioManager.playSound('crash');
        }
        
        return this.health <= 0;
    }
    
    addFuel(amount) {
        this.fuel = Math.min(100, this.fuel + amount);
    }
    
    addNitro(amount) {
        this.nitro = Math.min(100, this.nitro + amount);
    }
    
    addHealth(amount) {
        this.health = Math.min(CONFIG.PLAYER.MAX_HEALTH, this.health + amount);
    }
    
    draw(ctx, effects) {
        ctx.save();
        
        // Apply camera transform
        effects.applyCameraTransform(ctx);
        
        // Invulnerability flashing
        if (this.invulnerable && Math.floor(this.invulnerabilityTimer / 5) % 2) {
            ctx.globalAlpha = 0.5;
        }
        
        // Damage flash
        if (this.damageFlash > 0) {
            ctx.fillStyle = '#ff0000';
            ctx.globalAlpha = 0.3;
            ctx.fillRect(this.x - 5, this.y - 5, this.width + 10, this.height + 10);
            ctx.globalAlpha = 1;
        }
        
        // Car rotation
        ctx.translate(this.x + this.width / 2, this.y + this.height / 2);
        ctx.rotate(this.rotation * Math.PI / 180);
        ctx.translate(-this.width / 2, -this.height / 2);
        
        this.drawCar(ctx);
        
        // Reset camera transform
        effects.resetCameraTransform(ctx);
        
        ctx.restore();
    }
    
    drawCar(ctx) {
        // Car shadow
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fillRect(2, 2, this.width, this.height);
        
        // Car body
        ctx.fillStyle = this.isUsingNitro ? '#00ff88' : this.color;
        ctx.fillRect(0, 0, this.width, this.height);
        
        // Car details
        this.drawCarDetails(ctx);
        
        // Wheels
        this.drawWheels(ctx);
        
        // Headlights
        this.drawHeadlights(ctx);
        
        // Nitro glow
        if (this.isUsingNitro) {
            this.drawNitroGlow(ctx);
        }
    }
    
    drawCarDetails(ctx) {
        // Windows
        ctx.fillStyle = '#2c2c2c';
        ctx.fillRect(5, 8, this.width - 10, 12);
        ctx.fillRect(5, 25, this.width - 10, 15);
        ctx.fillRect(5, 45, this.width - 10, 10);
        
        // Hood details
        ctx.fillStyle = '#c0392b';
        ctx.fillRect(8, 2, this.width - 16, 4);
        
        // Side mirrors
        ctx.fillStyle = '#34495e';
        ctx.fillRect(-2, 15, 4, 6);
        ctx.fillRect(this.width - 2, 15, 4, 6);
    }
    
    drawWheels(ctx) {
        ctx.fillStyle = '#000';
        
        // Front wheels
        ctx.fillRect(-3, 8, 6, 12);
        ctx.fillRect(this.width - 3, 8, 6, 12);
        
        // Rear wheels
        ctx.fillRect(-3, 40, 6, 12);
        ctx.fillRect(this.width - 3, 40, 6, 12);
        
        // Wheel rims
        ctx.fillStyle = '#95a5a6';
        ctx.fillRect(-1, 10, 2, 8);
        ctx.fillRect(this.width - 1, 10, 2, 8);
        ctx.fillRect(-1, 42, 2, 8);
        ctx.fillRect(this.width - 1, 42, 2, 8);
    }
    
    drawHeadlights(ctx) {
        ctx.fillStyle = '#f1c40f';
        ctx.beginPath();
        ctx.arc(8, 2, 3, 0, Math.PI * 2);
        ctx.arc(this.width - 8, 2, 3, 0, Math.PI * 2);
        ctx.fill();
    }
    
    drawNitroGlow(ctx) {
        ctx.save();
        ctx.globalAlpha = 0.6;
        ctx.shadowColor = '#00ff88';
        ctx.shadowBlur = 15;
        ctx.strokeStyle = '#00ff88';
        ctx.lineWidth = 2;
        ctx.strokeRect(-2, -2, this.width + 4, this.height + 4);
        ctx.restore();
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
    
    getCenter() {
        return {
            x: this.x + this.width / 2,
            y: this.y + this.height / 2
        };
    }
}
