// Enemy Car Class
class EnemyCar {
    constructor(x, y, color, type = 'normal') {
        this.x = x;
        this.y = y;
        this.width = CONFIG.ENEMIES.WIDTH;
        this.height = CONFIG.ENEMIES.HEIGHT;
        this.color = color;
        this.type = type;
        this.speed = 0;
        this.health = this.getHealthByType();
        this.rotation = 0;
        this.exhaustTimer = 0;

        // حركة السيارة يمين ويسار
        this.horizontalSpeed = 0;
        this.targetX = x;
        this.movementTimer = 0;
        this.movementDirection = 0; // -1 يسار, 0 مستقيم, 1 يمين
        this.laneChangeTimer = 0;
        this.isChangingLane = false;
        this.originalLane = this.getCurrentLane();

        this.setupByType();
    }
    
    getHealthByType() {
        switch (this.type) {
            case 'truck': return 3;
            case 'sports': return 1;
            case 'police': return 2;
            default: return 1;
        }
    }
    
    setupByType() {
        switch (this.type) {
            case 'truck':
                this.width = 45;
                this.height = 80;
                this.color = '#8e44ad';
                break;
            case 'sports':
                this.width = 35;
                this.height = 55;
                this.color = '#e74c3c';
                break;
            case 'police':
                this.width = 40;
                this.height = 60;
                this.color = '#2980b9';
                break;
        }
    }
    
    getCurrentLane() {
        const lanes = CONFIG.CANVAS.LANES;
        let closestLane = 0;
        let minDistance = Math.abs(this.x + this.width/2 - lanes[0]);

        for (let i = 1; i < lanes.length; i++) {
            const distance = Math.abs(this.x + this.width/2 - lanes[i]);
            if (distance < minDistance) {
                minDistance = distance;
                closestLane = i;
            }
        }
        return closestLane;
    }

    update(gameSpeed, particles) {
        this.y += gameSpeed;
        this.movementTimer++;
        this.laneChangeTimer++;

        // حركة السيارة الذكية يمين ويسار
        this.updateHorizontalMovement();

        // تحديث الموقع الأفقي
        this.x += this.horizontalSpeed;

        // التأكد من عدم الخروج من الطريق
        this.x = Math.max(25, Math.min(CONFIG.CANVAS.WIDTH - this.width - 25, this.x));

        // تحديث الدوران حسب الحركة
        this.rotation = this.horizontalSpeed * 2;

        // Exhaust particles
        this.exhaustTimer++;
        if (this.exhaustTimer > 8) {
            particles.createExhaust(
                this.x + this.width / 2 + (Math.random() - 0.5) * 8,
                this.y + this.height,
                {
                    COUNT: 1,
                    LIFE: 15,
                    SPEED: 1,
                    COLORS: ['#95a5a6', '#bdc3c7']
                }
            );
            this.exhaustTimer = 0;
        }

        return this.y < CONFIG.CANVAS.HEIGHT + 50;
    }

    updateHorizontalMovement() {
        // تغيير المسار بشكل عشوائي
        if (Math.random() < CONFIG.ENEMIES.MOVEMENT_CHANCE && !this.isChangingLane) {
            this.startLaneChange();
        }

        // تنفيذ تغيير المسار
        if (this.isChangingLane) {
            const distance = this.targetX - this.x;
            if (Math.abs(distance) > 2) {
                this.horizontalSpeed = Math.sign(distance) * CONFIG.ENEMIES.LANE_CHANGE_SPEED;
            } else {
                this.x = this.targetX;
                this.horizontalSpeed = 0;
                this.isChangingLane = false;
            }
        } else {
            // حركة عشوائية صغيرة
            if (this.type === 'sports' && Math.random() < 0.01) {
                this.horizontalSpeed += (Math.random() - 0.5) * 0.5;
                this.horizontalSpeed = Math.max(-1, Math.min(1, this.horizontalSpeed));
            } else {
                this.horizontalSpeed *= 0.95; // تقليل الحركة تدريجياً
            }
        }
    }

    startLaneChange() {
        const lanes = CONFIG.CANVAS.LANES;
        const currentLane = this.getCurrentLane();
        const availableLanes = [];

        // البحث عن المسارات المتاحة
        if (currentLane > 0) availableLanes.push(currentLane - 1);
        if (currentLane < lanes.length - 1) availableLanes.push(currentLane + 1);

        if (availableLanes.length > 0) {
            const newLane = availableLanes[Math.floor(Math.random() * availableLanes.length)];
            this.targetX = lanes[newLane] - this.width / 2;
            this.isChangingLane = true;
            this.laneChangeTimer = 0;
        }
    }
    
    takeDamage(effects, particles) {
        this.health--;
        
        if (this.health <= 0) {
            // Create explosion
            particles.createExplosion(
                this.x + this.width / 2,
                this.y + this.height / 2
            );
            effects.addScreenShake(5, 10);
            return true; // Car destroyed
        } else {
            // Create sparks
            particles.createSparks(
                this.x + this.width / 2,
                this.y + this.height / 2
            );
            return false; // Car damaged but not destroyed
        }
    }
    
    draw(ctx) {
        ctx.save();
        
        // Car rotation
        ctx.translate(this.x + this.width / 2, this.y + this.height / 2);
        ctx.rotate(this.rotation * Math.PI / 180);
        ctx.translate(-this.width / 2, -this.height / 2);
        
        switch (this.type) {
            case 'truck':
                this.drawTruck(ctx);
                break;
            case 'sports':
                this.drawSportsCar(ctx);
                break;
            case 'police':
                this.drawPoliceCar(ctx);
                break;
            default:
                this.drawNormalCar(ctx);
        }
        
        ctx.restore();
    }
    
    drawNormalCar(ctx) {
        // Car shadow
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fillRect(2, 2, this.width, this.height);
        
        // Car body
        ctx.fillStyle = this.color;
        ctx.fillRect(0, 0, this.width, this.height);
        
        // Windows
        ctx.fillStyle = '#2c2c2c';
        ctx.fillRect(5, 8, this.width - 10, 12);
        ctx.fillRect(5, 35, this.width - 10, 15);
        
        // Wheels
        this.drawWheels(ctx);
        
        // Taillights
        ctx.fillStyle = '#e74c3c';
        ctx.fillRect(5, this.height - 3, 8, 3);
        ctx.fillRect(this.width - 13, this.height - 3, 8, 3);
    }
    
    drawTruck(ctx) {
        // Truck shadow
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fillRect(2, 2, this.width, this.height);
        
        // Truck body
        ctx.fillStyle = this.color;
        ctx.fillRect(0, 0, this.width, this.height);
        
        // Cab
        ctx.fillStyle = '#34495e';
        ctx.fillRect(5, 5, this.width - 10, 25);
        
        // Cargo area
        ctx.fillStyle = '#7f8c8d';
        ctx.fillRect(3, 30, this.width - 6, this.height - 35);
        
        // Windows
        ctx.fillStyle = '#2c2c2c';
        ctx.fillRect(8, 8, this.width - 16, 15);
        
        // Wheels (more wheels for truck)
        this.drawTruckWheels(ctx);
        
        // Taillights
        ctx.fillStyle = '#e74c3c';
        ctx.fillRect(5, this.height - 3, 8, 3);
        ctx.fillRect(this.width - 13, this.height - 3, 8, 3);
    }
    
    drawSportsCar(ctx) {
        // Car shadow
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fillRect(2, 2, this.width, this.height);
        
        // Car body (sleeker design)
        ctx.fillStyle = this.color;
        ctx.fillRect(0, 0, this.width, this.height);
        
        // Spoiler
        ctx.fillStyle = '#2c3e50';
        ctx.fillRect(8, this.height - 5, this.width - 16, 3);
        
        // Windows (smaller, sportier)
        ctx.fillStyle = '#1a1a1a';
        ctx.fillRect(6, 8, this.width - 12, 10);
        ctx.fillRect(6, 30, this.width - 12, 12);
        
        // Racing stripes
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(this.width / 2 - 1, 0, 2, this.height);
        
        // Wheels
        this.drawWheels(ctx);
        
        // Taillights (LED style)
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(3, this.height - 2, 10, 2);
        ctx.fillRect(this.width - 13, this.height - 2, 10, 2);
    }
    
    drawPoliceCar(ctx) {
        // Car shadow
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.fillRect(2, 2, this.width, this.height);
        
        // Car body
        ctx.fillStyle = this.color;
        ctx.fillRect(0, 0, this.width, this.height);
        
        // Police markings
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 15, this.width, 8);
        ctx.fillRect(0, 35, this.width, 8);
        
        // Police text
        ctx.fillStyle = '#2c3e50';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('POLICE', this.width / 2, 20);
        
        // Light bar
        ctx.fillStyle = '#34495e';
        ctx.fillRect(5, 2, this.width - 10, 4);
        
        // Flashing lights
        const flashColor = Math.floor(Date.now() / 200) % 2 ? '#e74c3c' : '#3498db';
        ctx.fillStyle = flashColor;
        ctx.fillRect(8, 2, 6, 4);
        ctx.fillRect(this.width - 14, 2, 6, 4);
        
        // Windows
        ctx.fillStyle = '#2c2c2c';
        ctx.fillRect(5, 8, this.width - 10, 12);
        ctx.fillRect(5, 45, this.width - 10, 10);
        
        // Wheels
        this.drawWheels(ctx);
        
        // Taillights
        ctx.fillStyle = '#e74c3c';
        ctx.fillRect(5, this.height - 3, 8, 3);
        ctx.fillRect(this.width - 13, this.height - 3, 8, 3);
    }
    
    drawWheels(ctx) {
        ctx.fillStyle = '#000';
        
        // Front wheels
        ctx.fillRect(-3, 8, 6, 12);
        ctx.fillRect(this.width - 3, 8, 6, 12);
        
        // Rear wheels
        ctx.fillRect(-3, this.height - 20, 6, 12);
        ctx.fillRect(this.width - 3, this.height - 20, 6, 12);
        
        // Wheel rims
        ctx.fillStyle = '#95a5a6';
        ctx.fillRect(-1, 10, 2, 8);
        ctx.fillRect(this.width - 1, 10, 2, 8);
        ctx.fillRect(-1, this.height - 18, 2, 8);
        ctx.fillRect(this.width - 1, this.height - 18, 2, 8);
    }
    
    drawTruckWheels(ctx) {
        ctx.fillStyle = '#000';
        
        // Front wheels
        ctx.fillRect(-4, 15, 8, 15);
        ctx.fillRect(this.width - 4, 15, 8, 15);
        
        // Middle wheels
        ctx.fillRect(-4, 35, 8, 15);
        ctx.fillRect(this.width - 4, 35, 8, 15);
        
        // Rear wheels
        ctx.fillRect(-4, this.height - 20, 8, 15);
        ctx.fillRect(this.width - 4, this.height - 20, 8, 15);
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
    
    getCenter() {
        return {
            x: this.x + this.width / 2,
            y: this.y + this.height / 2
        };
    }
}

// Enemy Manager
class EnemyManager {
    constructor() {
        this.enemies = [];
        this.spawnTimer = 0;
        this.spawnInterval = 60;
        this.difficultyMultiplier = 1;
    }
    
    update(gameSpeed, particles, difficulty) {
        this.difficultyMultiplier = CONFIG.DIFFICULTY[difficulty.toUpperCase()].SPAWN_RATE_MULTIPLIER;
        
        // Update existing enemies
        this.enemies = this.enemies.filter(enemy => enemy.update(gameSpeed, particles));
        
        // Spawn new enemies
        this.spawnTimer++;
        const adjustedInterval = this.spawnInterval / this.difficultyMultiplier;
        
        if (this.spawnTimer >= adjustedInterval) {
            this.spawnEnemy();
            this.spawnTimer = 0;
            this.spawnInterval = CONFIG.ENEMIES.MIN_SPAWN_INTERVAL + 
                               Math.random() * (CONFIG.ENEMIES.MAX_SPAWN_INTERVAL - CONFIG.ENEMIES.MIN_SPAWN_INTERVAL);
        }
    }
    
    spawnEnemy() {
        const lanes = CONFIG.CANVAS.LANES;
        const lane = lanes[Math.floor(Math.random() * lanes.length)];
        const color = CONFIG.ENEMIES.COLORS[Math.floor(Math.random() * CONFIG.ENEMIES.COLORS.length)];
        
        // Determine enemy type based on probability
        let type = 'normal';
        const rand = Math.random();
        if (rand < 0.1) {
            type = 'truck';
        } else if (rand < 0.25) {
            type = 'sports';
        } else if (rand < 0.35) {
            type = 'police';
        }
        
        const enemy = new EnemyCar(
            lane - CONFIG.ENEMIES.WIDTH / 2,
            -CONFIG.ENEMIES.HEIGHT,
            color,
            type
        );
        
        this.enemies.push(enemy);
    }
    
    checkCollisions(player, effects, particles) {
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            
            if (this.isColliding(player.getBounds(), enemy.getBounds())) {
                // Player takes damage
                const damage = CONFIG.PLAYER.DAMAGE_PER_HIT;
                const gameOver = player.takeDamage(damage, effects, particles);
                
                // Enemy takes damage too
                const enemyDestroyed = enemy.takeDamage(effects, particles);
                
                if (enemyDestroyed) {
                    this.enemies.splice(i, 1);
                }
                
                return gameOver;
            }
        }
        
        return false;
    }
    
    isColliding(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }
    
    draw(ctx) {
        this.enemies.forEach(enemy => enemy.draw(ctx));
    }
    
    clear() {
        this.enemies = [];
    }
    
    getEnemyCount() {
        return this.enemies.length;
    }
}
