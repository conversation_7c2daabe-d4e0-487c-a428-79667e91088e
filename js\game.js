// Main Game Class
class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gameState = GAME_STATES.START;
        this.difficulty = 'normal';
        
        // Game stats
        this.score = 0;
        this.highScore = localStorage.getItem('carGameHighScore') || 0;
        this.level = 1;
        this.gameSpeed = CONFIG.GAME.INITIAL_SPEED;
        this.frameCount = 0;
        this.startTime = 0;
        this.distance = 0;
        this.coinsCollected = 0;
        
        // Input handling
        this.keys = {};
        
        // Initialize game systems
        this.initializeSystems();
        this.setupEventListeners();
        
        // Start with menu screen
        this.ui.showScreen('start');
        this.ui.updateHighScore(this.highScore);
    }
    
    initializeSystems() {
        // Initialize all game systems
        this.audioManager = new AudioManager();
        this.particleSystem = new ParticleSystem();
        this.weatherSystem = new WeatherSystem();
        this.effects = new EffectsManager();
        this.background = new BackgroundManager();
        this.lighting = new LightingManager();
        this.ui = new UIManager();
        
        // Initialize game objects
        this.player = new Player();
        this.enemyManager = new EnemyManager();
        this.collectiblesManager = new CollectiblesManager();
        this.obstaclesManager = new ObstaclesManager();
        
        // Make audio manager globally available
        window.audioManager = this.audioManager;
        window.selectedDifficulty = 'normal';
    }
    
    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (e) => {
            this.keys[e.key.toLowerCase()] = true;
            this.handleKeyDown(e);
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.key.toLowerCase()] = false;
        });
        
        // Button events
        this.ui.elements.startBtn.addEventListener('click', () => this.startGame());
        this.ui.elements.restartBtn.addEventListener('click', () => this.restartGame());
        
        // Prevent context menu on canvas
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
        
        // Handle window focus/blur for auto-pause
        window.addEventListener('blur', () => {
            if (this.gameState === GAME_STATES.PLAYING) {
                this.pauseGame();
            }
        });
    }
    
    handleKeyDown(e) {
        if (KEYS.PAUSE.includes(e.key)) {
            e.preventDefault();
            if (this.gameState === GAME_STATES.PLAYING) {
                this.pauseGame();
            } else if (this.gameState === GAME_STATES.PAUSED) {
                this.resumeGame();
            }
        }
    }
    
    startGame() {
        this.gameState = GAME_STATES.PLAYING;
        this.difficulty = window.selectedDifficulty || 'normal';
        this.resetGameStats();
        this.resetGameObjects();
        
        this.ui.showScreen('none');
        this.audioManager.startEngineSound();
        this.audioManager.playMusic('background');
        
        this.startTime = Date.now();
        this.gameLoop();
    }
    
    resetGameStats() {
        this.score = 0;
        this.level = 1;
        this.gameSpeed = CONFIG.GAME.INITIAL_SPEED;
        this.frameCount = 0;
        this.distance = 0;
        this.coinsCollected = 0;
    }
    
    resetGameObjects() {
        this.player.reset();
        this.enemyManager.clear();
        this.collectiblesManager.clear();
        this.obstaclesManager.clear();
        this.particleSystem.clear();
        this.weatherSystem.clearWeather();
        this.effects = new EffectsManager();
    }
    
    pauseGame() {
        this.gameState = GAME_STATES.PAUSED;
        this.ui.showScreen('pause');
        this.audioManager.stopEngineSound();
    }
    
    resumeGame() {
        this.gameState = GAME_STATES.PLAYING;
        this.ui.showScreen('none');
        this.audioManager.startEngineSound();
        this.gameLoop();
    }
    
    restartGame() {
        this.startGame();
    }
    
    gameOver() {
        this.gameState = GAME_STATES.GAME_OVER;
        
        // Calculate final stats
        const playTime = Math.floor((Date.now() - this.startTime) / 1000);
        const finalStats = {
            score: this.score,
            level: this.level,
            distance: Math.floor(this.distance),
            coins: this.coinsCollected,
            time: playTime
        };
        
        // Update high score
        if (this.score > this.highScore) {
            this.highScore = this.score;
            localStorage.setItem('carGameHighScore', this.highScore);
            this.ui.updateHighScore(this.highScore);
        }
        
        // Show game over screen
        this.ui.updateFinalStats(finalStats);
        this.ui.showScreen('gameOver');
        
        // Stop audio
        this.audioManager.stopEngineSound();
        this.audioManager.stopMusic('background');
        
        // Add explosion effect
        this.effects.addScreenShake(15, 30);
        this.effects.addFlash('#ff0000', 0.8, 20);
    }
    
    update() {
        if (this.gameState !== GAME_STATES.PLAYING) return;
        
        this.frameCount++;
        
        // Update game speed and level
        this.updateGameProgression();
        
        // Update all game systems
        this.player.update(this.keys, this.gameSpeed, this.effects, this.particleSystem);
        this.enemyManager.update(this.gameSpeed, this.particleSystem, this.difficulty);
        this.collectiblesManager.update(this.gameSpeed, this.particleSystem);
        this.obstaclesManager.update(this.gameSpeed);
        this.particleSystem.update();
        this.weatherSystem.update();
        this.effects.update();
        this.background.update(this.gameSpeed);
        this.lighting.update(this.gameSpeed);
        
        // Check collisions
        this.checkCollisions();
        
        // Update UI
        this.updateUI();
        
        // Update distance
        this.distance += this.gameSpeed;
        
        // Update engine sound
        this.audioManager.updateEngineSound(this.gameSpeed);
        
        // Check game over conditions
        if (this.player.health <= 0 || this.player.fuel <= 0) {
            this.gameOver();
        }
    }
    
    updateGameProgression() {
        // Increase game speed gradually
        this.gameSpeed = Math.min(
            CONFIG.GAME.MAX_SPEED,
            CONFIG.GAME.INITIAL_SPEED + (this.score * CONFIG.GAME.SPEED_INCREMENT)
        );
        
        // Level up system
        const newLevel = Math.floor(this.score / CONFIG.GAME.LEVEL_SCORE_THRESHOLD) + 1;
        if (newLevel > this.level) {
            this.level = newLevel;
            this.ui.showLevelUpEffect(this.level);
            this.ui.addNotification(`مستوى جديد: ${this.level}!`, 'success');
            
            // Level up rewards
            this.player.addHealth(25);
            this.player.addFuel(30);
            this.player.addNitro(50);
        }
    }
    
    checkCollisions() {
        // Enemy collisions
        if (this.enemyManager.checkCollisions(this.player, this.effects, this.particleSystem)) {
            return; // Game over
        }
        
        // Collectible collisions
        const coinsEarned = this.collectiblesManager.checkCollisions(
            this.player, this.effects, this.particleSystem
        );
        if (coinsEarned > 0) {
            this.score += coinsEarned;
            this.coinsCollected += coinsEarned / CONFIG.GAME.SCORE_PER_COIN;
            this.player.addNitro(CONFIG.GAME.NITRO_GAIN);
        }
        
        // Obstacle collisions
        if (this.obstaclesManager.checkCollisions(this.player, this.effects, this.particleSystem)) {
            return; // Game over
        }
        
        // Score for surviving
        if (this.frameCount % 60 === 0) { // Every second
            this.score += 1;
        }
    }
    
    updateUI() {
        const gameData = {
            score: this.score,
            level: this.level,
            speed: Math.floor(this.gameSpeed * 20),
            fuel: this.player.fuel,
            nitro: this.player.nitro,
            health: this.player.health
        };

        this.ui.updateScore(gameData.score);
        this.ui.updateSpeed(gameData.speed);
        this.ui.updateLevel(gameData.level);
        this.ui.updateFuel(gameData.fuel);
        this.ui.updateNitro(gameData.nitro);
        this.ui.updateHealth(gameData.health);
        this.ui.updatePowerUps(this.player);
        this.ui.updateCombo(this.player);
        this.ui.updateNotifications();
        this.ui.updateMiniMap(
            this.player,
            this.enemyManager.enemies,
            this.collectiblesManager.collectibles
        );

        // تحديث واجهة الشاشة الكاملة
        this.ui.updateFullscreenUI(gameData);
    }
    
    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Apply camera effects
        this.effects.applyCameraTransform(this.ctx);
        
        // Draw background and road
        this.background.draw(this.ctx);
        
        // Draw weather effects
        this.weatherSystem.draw(this.ctx);
        
        // Draw game objects
        this.obstaclesManager.draw(this.ctx);
        this.collectiblesManager.draw(this.ctx);
        this.enemyManager.draw(this.ctx);
        this.player.draw(this.ctx, this.effects);
        
        // Draw particles
        this.particleSystem.draw(this.ctx);
        
        // Draw lighting effects
        this.lighting.drawStreetLights(this.ctx);
        this.lighting.drawHeadlights(this.ctx, this.player.x, this.player.y);
        
        // Reset camera effects
        this.effects.resetCameraTransform(this.ctx);
        
        // Draw screen effects
        this.effects.drawFlash(this.ctx);
        
        // Draw UI notifications
        this.ui.drawNotifications(this.ctx);
    }
    
    gameLoop() {
        if (this.gameState === GAME_STATES.PLAYING) {
            this.update();
            this.draw();
            requestAnimationFrame(() => this.gameLoop());
        }
    }
}
